import 'package:flutter/material.dart';
import '../utils/theme.dart';

class StarRating extends StatelessWidget {
  final int rating;
  final int maxRating;
  final double size;
  final Color? activeColor;
  final Color? inactiveColor;
  final bool allowHalfRating;
  final double spacing;
  final VoidCallback? onTap;

  const StarRating({
    super.key,
    required this.rating,
    this.maxRating = 5,
    this.size = 16,
    this.activeColor,
    this.inactiveColor,
    this.allowHalfRating = false,
    this.spacing = 2,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final activeStarColor = activeColor ?? AppTheme.warningLight;
    final inactiveStarColor = inactiveColor ?? AppTheme.dividerLight;

    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(maxRating, (index) {
          return Padding(
            padding: EdgeInsets.only(right: index < maxRating - 1 ? spacing : 0),
            child: Icon(
              index < rating ? Icons.star : Icons.star_border,
              size: size,
              color: index < rating ? activeStarColor : inactiveStarColor,
            ),
          );
        }),
      ),
    );
  }
}

class InteractiveStarRating extends StatefulWidget {
  final int initialRating;
  final int maxRating;
  final double size;
  final Color? activeColor;
  final Color? inactiveColor;
  final double spacing;
  final Function(int rating)? onRatingChanged;
  final bool enabled;

  const InteractiveStarRating({
    super.key,
    this.initialRating = 0,
    this.maxRating = 5,
    this.size = 24,
    this.activeColor,
    this.inactiveColor,
    this.spacing = 4,
    this.onRatingChanged,
    this.enabled = true,
  });

  @override
  State<InteractiveStarRating> createState() => _InteractiveStarRatingState();
}

class _InteractiveStarRatingState extends State<InteractiveStarRating> {
  late int _currentRating;

  @override
  void initState() {
    super.initState();
    _currentRating = widget.initialRating;
  }

  @override
  Widget build(BuildContext context) {
    final activeStarColor = widget.activeColor ?? AppTheme.warningLight;
    final inactiveStarColor = widget.inactiveColor ?? AppTheme.dividerLight;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(widget.maxRating, (index) {
        return GestureDetector(
          onTap: widget.enabled
              ? () {
                  setState(() {
                    _currentRating = index + 1;
                  });
                  widget.onRatingChanged?.call(_currentRating);
                }
              : null,
          child: Padding(
            padding: EdgeInsets.only(
              right: index < widget.maxRating - 1 ? widget.spacing : 0,
            ),
            child: Icon(
              index < _currentRating ? Icons.star : Icons.star_border,
              size: widget.size,
              color: index < _currentRating ? activeStarColor : inactiveStarColor,
            ),
          ),
        );
      }),
    );
  }
}

// Compact star rating for lists
class CompactStarRating extends StatelessWidget {
  final int rating;
  final int maxRating;
  final double size;
  final Color? color;

  const CompactStarRating({
    super.key,
    required this.rating,
    this.maxRating = 5,
    this.size = 14,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final starColor = color ?? AppTheme.warningLight;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(maxRating, (index) {
        return Icon(
          index < rating ? Icons.star : Icons.star_border,
          size: size,
          color: index < rating ? starColor : AppTheme.dividerLight,
        );
      }),
    );
  }
}

// Star rating with text
class StarRatingWithText extends StatelessWidget {
  final int rating;
  final int maxRating;
  final double starSize;
  final double textSize;
  final Color? starColor;
  final Color? textColor;
  final String? customText;
  final bool showRatingNumber;

  const StarRatingWithText({
    super.key,
    required this.rating,
    this.maxRating = 5,
    this.starSize = 16,
    this.textSize = 12,
    this.starColor,
    this.textColor,
    this.customText,
    this.showRatingNumber = false,
  });

  @override
  Widget build(BuildContext context) {
    final starActiveColor = starColor ?? AppTheme.warningLight;
    final textDisplayColor = textColor ?? AppTheme.textSecondaryLight;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        StarRating(
          rating: rating,
          maxRating: maxRating,
          size: starSize,
          activeColor: starActiveColor,
        ),
        if (showRatingNumber || customText != null) ...[
          const SizedBox(width: 4),
          Text(
            customText ?? '$rating/$maxRating',
            style: TextStyle(
              fontSize: textSize,
              color: textDisplayColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }
}

// Utility functions for star ratings
class StarRatingUtils {
  static String getRatingText(int rating) {
    switch (rating) {
      case 1:
        return 'Poor';
      case 2:
        return 'Fair';
      case 3:
        return 'Good';
      case 4:
        return 'Very Good';
      case 5:
        return 'Excellent';
      default:
        return 'Not Rated';
    }
  }

  static Color getRatingColor(int rating) {
    switch (rating) {
      case 1:
        return AppTheme.errorLight;
      case 2:
        return Colors.orange;
      case 3:
        return AppTheme.warningLight;
      case 4:
        return Colors.lightGreen;
      case 5:
        return AppTheme.successLight;
      default:
        return AppTheme.textSecondaryLight;
    }
  }

  static IconData getRatingIcon(int rating) {
    switch (rating) {
      case 1:
        return Icons.sentiment_very_dissatisfied;
      case 2:
        return Icons.sentiment_dissatisfied;
      case 3:
        return Icons.sentiment_neutral;
      case 4:
        return Icons.sentiment_satisfied;
      case 5:
        return Icons.sentiment_very_satisfied;
      default:
        return Icons.help_outline;
    }
  }
}
