import 'package:flutter_test/flutter_test.dart';
import 'package:domain_flutter_code/models/category.dart';

void main() {
  group('Category Model Tests', () {
    test('should create Category from JSON with all fields', () {
      // Arrange
      final json = {
        'id': 1,
        'name': 'Technology',
        'color': '#FF5722',
        'created_at': '2024-01-01T00:00:00Z',
        'updated_at': '2024-01-01T00:00:00Z',
        'total_domains': 15,
        'buy_domains_count': 10,
        'reserve_domains_count': 5,
      };

      // Act
      final category = Category.fromJson(json);

      // Assert
      expect(category.id, 1);
      expect(category.name, 'Technology');
      expect(category.color, '#FF5722');
      expect(category.totalDomains, 15);
      expect(category.buyDomainsCount, 10);
      expect(category.reserveDomainsCount, 5);
    });

    test('should handle missing domain counts gracefully', () {
      // Arrange
      final json = {
        'id': 2,
        'name': 'Business',
        'color': '#2196F3',
        'created_at': '2024-01-01T00:00:00Z',
        'updated_at': '2024-01-01T00:00:00Z',
        // Missing domain count fields
      };

      // Act
      final category = Category.fromJson(json);

      // Assert
      expect(category.id, 2);
      expect(category.name, 'Business');
      expect(category.totalDomains, 0);
      expect(category.buyDomainsCount, 0);
      expect(category.reserveDomainsCount, 0);
    });

    test('should convert Category to JSON correctly', () {
      // Arrange
      final category = Category(
        id: 3,
        name: 'Entertainment',
        color: '#9C27B0',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        totalDomains: 8,
        buyDomainsCount: 3,
        reserveDomainsCount: 5,
      );

      // Act
      final json = category.toJson();

      // Assert
      expect(json['id'], 3);
      expect(json['name'], 'Entertainment');
      expect(json['color'], '#9C27B0');
      expect(json['total_domains'], 8);
      expect(json['buy_domains_count'], 3);
      expect(json['reserve_domains_count'], 5);
    });

    test('should handle zero domain counts', () {
      // Arrange
      final json = {
        'id': 4,
        'name': 'Empty Category',
        'color': '#607D8B',
        'created_at': '2024-01-01T00:00:00Z',
        'updated_at': '2024-01-01T00:00:00Z',
        'total_domains': 0,
        'buy_domains_count': 0,
        'reserve_domains_count': 0,
      };

      // Act
      final category = Category.fromJson(json);

      // Assert
      expect(category.totalDomains, 0);
      expect(category.buyDomainsCount, 0);
      expect(category.reserveDomainsCount, 0);
    });
  });
}
