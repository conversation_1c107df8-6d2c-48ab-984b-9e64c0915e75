import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:shimmer/shimmer.dart';
import '../utils/theme.dart';

enum LoadingType {
  circular,
  linear,
  dots,
  pulse,
  shimmer,
  skeleton,
}

class EnhancedLoading extends StatelessWidget {
  final LoadingType type;
  final String? message;
  final Color? color;
  final double? size;
  final bool showMessage;

  const EnhancedLoading({
    super.key,
    this.type = LoadingType.circular,
    this.message,
    this.color,
    this.size,
    this.showMessage = true,
  });

  const EnhancedLoading.circular({
    super.key,
    this.message,
    this.color,
    this.size,
    this.showMessage = true,
  }) : type = LoadingType.circular;

  const EnhancedLoading.dots({
    super.key,
    this.message,
    this.color,
    this.size,
    this.showMessage = true,
  }) : type = LoadingType.dots;

  const EnhancedLoading.pulse({
    super.key,
    this.message,
    this.color,
    this.size,
    this.showMessage = true,
  }) : type = LoadingType.pulse;

  @override
  Widget build(BuildContext context) {
    final loadingColor = color ?? AppTheme.primaryLight;
    final loadingSize = size ?? 40.0;

    Widget loadingWidget;

    switch (type) {
      case LoadingType.circular:
        loadingWidget = SizedBox(
          width: loadingSize,
          height: loadingSize,
          child: CircularProgressIndicator(
            strokeWidth: 3,
            valueColor: AlwaysStoppedAnimation<Color>(loadingColor),
          ),
        );
        break;

      case LoadingType.linear:
        loadingWidget = SizedBox(
          width: loadingSize * 2,
          child: LinearProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(loadingColor),
            backgroundColor: loadingColor.withValues(alpha: 0.2),
          ),
        );
        break;

      case LoadingType.dots:
        loadingWidget = _DotsLoading(
          color: loadingColor,
          size: loadingSize / 8,
        );
        break;

      case LoadingType.pulse:
        loadingWidget = _PulseLoading(
          color: loadingColor,
          size: loadingSize,
        );
        break;

      case LoadingType.shimmer:
        loadingWidget = _ShimmerLoading(size: loadingSize);
        break;

      case LoadingType.skeleton:
        loadingWidget = _SkeletonLoading(size: loadingSize);
        break;
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        loadingWidget,
        if (showMessage && message != null) ...[
          const SizedBox(height: AppTheme.spaceMD),
          Text(
            message!,
            style: TextStyle(
              color: AppTheme.textSecondaryLight,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

class _DotsLoading extends StatelessWidget {
  final Color color;
  final double size;

  const _DotsLoading({
    required this.color,
    required this.size,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return Container(
          width: size,
          height: size,
          margin: EdgeInsets.symmetric(horizontal: size / 4),
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        )
            .animate(onPlay: (controller) => controller.repeat())
            .scale(
              begin: const Offset(0.5, 0.5),
              end: const Offset(1.2, 1.2),
              duration: 600.ms,
              delay: (index * 200).ms,
            )
            .then()
            .scale(
              begin: const Offset(1.2, 1.2),
              end: const Offset(0.5, 0.5),
              duration: 600.ms,
            );
      }),
    );
  }
}

class _PulseLoading extends StatelessWidget {
  final Color color;
  final double size;

  const _PulseLoading({
    required this.color,
    required this.size,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    )
        .animate(onPlay: (controller) => controller.repeat())
        .scale(
          begin: const Offset(0.8, 0.8),
          end: const Offset(1.2, 1.2),
          duration: 1000.ms,
        )
        .fadeIn(duration: 500.ms)
        .then()
        .fadeOut(duration: 500.ms);
  }
}

class _ShimmerLoading extends StatelessWidget {
  final double size;

  const _ShimmerLoading({required this.size});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppTheme.dividerLight,
      highlightColor: AppTheme.surfaceLight,
      child: Container(
        width: size * 2,
        height: size,
        decoration: BoxDecoration(
          color: AppTheme.dividerLight,
          borderRadius: AppTheme.smallRadius,
        ),
      ),
    );
  }
}

class _SkeletonLoading extends StatelessWidget {
  final double size;

  const _SkeletonLoading({required this.size});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _SkeletonItem(width: size * 2, height: size / 4),
        const SizedBox(height: AppTheme.spaceSM),
        _SkeletonItem(width: size * 1.5, height: size / 4),
        const SizedBox(height: AppTheme.spaceSM),
        _SkeletonItem(width: size * 1.8, height: size / 4),
      ],
    );
  }
}

class _SkeletonItem extends StatelessWidget {
  final double width;
  final double height;

  const _SkeletonItem({
    required this.width,
    required this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppTheme.dividerLight,
      highlightColor: AppTheme.surfaceLight,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: AppTheme.dividerLight,
          borderRadius: AppTheme.smallRadius,
        ),
      ),
    );
  }
}

// Full screen loading overlay
class LoadingOverlay extends StatelessWidget {
  final bool isLoading;
  final Widget child;
  final String? message;
  final LoadingType type;

  const LoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.message,
    this.type = LoadingType.circular,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: Colors.black.withValues(alpha: 0.3),
            child: Center(
              child: Container(
                padding: const EdgeInsets.all(AppTheme.spaceLG),
                decoration: BoxDecoration(
                  color: AppTheme.surfaceLight,
                  borderRadius: AppTheme.largeRadius,
                  boxShadow: AppTheme.elevatedShadow,
                ),
                child: EnhancedLoading(
                  type: type,
                  message: message ?? 'Loading...',
                ),
              ),
            ),
          ).animate().fadeIn(duration: 200.ms),
      ],
    );
  }
}

// Skeleton loading for lists
class SkeletonList extends StatelessWidget {
  final int itemCount;
  final double itemHeight;
  final EdgeInsets? padding;

  const SkeletonList({
    super.key,
    this.itemCount = 5,
    this.itemHeight = 80,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: padding,
      itemCount: itemCount,
      itemBuilder: (context, index) {
        return Container(
          height: itemHeight,
          margin: const EdgeInsets.symmetric(
            horizontal: AppTheme.spaceMD,
            vertical: AppTheme.spaceSM,
          ),
          child: Shimmer.fromColors(
            baseColor: AppTheme.dividerLight,
            highlightColor: AppTheme.surfaceLight,
            child: Container(
              decoration: BoxDecoration(
                color: AppTheme.dividerLight,
                borderRadius: AppTheme.mediumRadius,
              ),
            ),
          ),
        );
      },
    );
  }
}
