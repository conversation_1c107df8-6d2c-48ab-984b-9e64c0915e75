import 'package:flutter/material.dart';
import '../models/admin_profile.dart';
import '../models/expiry_settings.dart';
import '../services/api_service.dart';

class SettingsProvider with ChangeNotifier {
  AdminProfile? _adminProfile;
  ExpirySettings? _expirySettings;
  Map<String, dynamic>? _systemSettings;
  bool _isLoading = false;
  String? _error;

  AdminProfile? get adminProfile => _adminProfile;
  ExpirySettings? get expirySettings => _expirySettings;
  Map<String, dynamic>? get systemSettings => _systemSettings;
  bool get isLoading => _isLoading;
  String? get error => _error;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  // Admin Profile Methods
  Future<bool> loadAdminProfile() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await ApiService.getAdminProfile();
      
      if (response['success'] == true) {
        _adminProfile = AdminProfile.fromJson(response['data']);
        return true;
      }
      return false;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateAdminProfile(Map<String, dynamic> data) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await ApiService.updateAdminProfile(data);
      
      if (response['success'] == true) {
        _adminProfile = AdminProfile.fromJson(response['data']);
        return true;
      }
      return false;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Expiry Settings Methods
  Future<bool> loadExpirySettings() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await ApiService.getExpirySettings();
      
      if (response['success'] == true) {
        _expirySettings = ExpirySettings.fromJson(response['data']);
        return true;
      }
      return false;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateExpirySettings(Map<String, dynamic> data) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await ApiService.updateExpirySettings(data);
      
      if (response['success'] == true) {
        _expirySettings = ExpirySettings.fromJson(response['data']);
        return true;
      }
      return false;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> testEmail(String testEmail) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await ApiService.testEmail(testEmail);
      
      if (response['success'] == true) {
        return true;
      }
      _setError(response['message'] ?? 'Failed to send test email');
      return false;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // System Settings Methods
  Future<bool> loadSystemSettings() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await ApiService.getSystemSettings();
      
      if (response['success'] == true) {
        _systemSettings = response['data'];
        return true;
      }
      return false;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Load all settings
  Future<bool> loadAllSettings() async {
    _setLoading(true);
    _clearError();

    try {
      final results = await Future.wait([
        loadAdminProfile(),
        loadExpirySettings(),
        loadSystemSettings(),
      ]);

      return results.every((result) => result);
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Clear all data
  void clearSettings() {
    _adminProfile = null;
    _expirySettings = null;
    _systemSettings = null;
    _error = null;
    notifyListeners();
  }
}
