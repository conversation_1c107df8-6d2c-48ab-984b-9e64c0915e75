@extends('layouts.app')

@section('title', 'Admin Dashboard - Domain CRM')

@section('content')
<div class="row mb-4">
    <!-- Stats Cards -->
    <div class="col-md-3 mb-4">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-globe fa-3x mb-3"></i>
                <h2 class="mb-1">{{ $stats['total_domains'] }}</h2>
                <p class="mb-0">Total Purchase</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-tags fa-3x mb-3"></i>
                <h2 class="mb-1">{{ $stats['total_categories'] }}</h2>
                <p class="mb-0">Categories</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                <h2 class="mb-1">{{ $stats['expiring_domains'] }}</h2>
                <p class="mb-0">Expiring Soon</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-shield-alt fa-3x mb-3"></i>
                <h2 class="mb-1">{{ Auth::guard('admin')->user()->role === 'super_admin' ? 'Super' : 'Admin' }}</h2>
                <p class="mb-0">Access Level</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Quick Actions -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2 text-warning"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('domains.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Add New Domain
                    </a>
                    <a href="{{ route('categories.create') }}" class="btn btn-outline-primary">
                        <i class="fas fa-tag me-2"></i>
                        Create Category
                    </a>
                    <a href="{{ route('domains.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-2"></i>
                        View All Domains
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Domains -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2 text-info"></i>
                    Recent Domains
                </h5>
                <a href="{{ route('domains.index') }}" class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="card-body">
                @if($stats['recent_domains']->count() > 0)
                    @foreach($stats['recent_domains'] as $domain)
                        <div class="d-flex justify-content-between align-items-center mb-3 p-2 rounded" 
                             style="background-color: #f8fafc;">
                            <div>
                                <strong>{{ $domain->name }}</strong>
                                <br>
                                <small class="text-muted">{{ $domain->created_at->diffForHumans() }}</small>
                            </div>
                            <div class="text-end">
                                <div class="star-rating" style="color: #fbbf24;">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star{{ $i <= $domain->rating ? '' : '-o' }}"></i>
                                    @endfor
                                </div>
                            </div>
                        </div>
                    @endforeach
                @else
                    <p class="text-muted text-center">No recent domains found</p>
                @endif
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- System Information -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2 text-primary"></i>
                    System Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <strong>Laravel Version:</strong>
                    </div>
                    <div class="col-6">
                        {{ app()->version() }}
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6">
                        <strong>PHP Version:</strong>
                    </div>
                    <div class="col-6">
                        {{ PHP_VERSION }}
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6">
                        <strong>Environment:</strong>
                    </div>
                    <div class="col-6">
                        <span class="badge {{ app()->environment() === 'production' ? 'bg-success' : 'bg-warning' }}">
                            {{ ucfirst(app()->environment()) }}
                        </span>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6">
                        <strong>Debug Mode:</strong>
                    </div>
                    <div class="col-6">
                        <span class="badge {{ config('app.debug') ? 'bg-warning' : 'bg-success' }}">
                            {{ config('app.debug') ? 'Enabled' : 'Disabled' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Admin Profile -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-shield me-2 text-success"></i>
                    Admin Profile
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="user-avatar me-3" style="width: 60px; height: 60px; font-size: 1.5rem;">
                        {{ substr(Auth::guard('admin')->user()->name, 0, 1) }}
                    </div>
                    <div>
                        <h6 class="mb-1">{{ Auth::guard('admin')->user()->name }}</h6>
                        <p class="text-muted mb-1">{{ Auth::guard('admin')->user()->email }}</p>
                        <span class="badge bg-primary">{{ ucfirst(str_replace('_', ' ', Auth::guard('admin')->user()->role)) }}</span>
                    </div>
                </div>
                
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.settings') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit me-2"></i>
                        Edit Profile
                    </a>
                    <a href="{{ route('admin.system-settings') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-cogs me-2"></i>
                        System Settings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Activity Log (Future Enhancement) -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2 text-secondary"></i>
                    Recent Activity
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center py-4">
                    <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">Activity Logging</h6>
                    <p class="text-muted">Activity logging feature will be available in future updates.</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Add any dashboard-specific JavaScript here
    console.log('Admin Dashboard Loaded');
</script>
@endpush