import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/simple_domain.dart';
import '../providers/category_provider.dart';

class SimpleDomainCard extends StatelessWidget {
  final SimpleDomain simpleDomain;
  final VoidCallback? onBuy;

  const SimpleDomainCard({super.key, required this.simpleDomain, this.onBuy});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    simpleDomain.name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (onBuy != null)
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      gradient: const LinearGradient(
                        colors: [Color(0xFF4CAF50), Color(0xFF45A049)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
                          blurRadius: 6,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: ElevatedButton(
                      onPressed: onBuy,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        foregroundColor: Colors.white,
                        shadowColor: Colors.transparent,
                        padding: const EdgeInsets.all(12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                        minimumSize: const Size(48, 48),
                      ),
                      child: const Icon(Icons.shopping_cart_rounded, size: 16),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Consumer<CategoryProvider>(
              builder: (context, categoryProvider, child) {
                final categories = categoryProvider.getCategoriesByIds(
                  simpleDomain.categories,
                );
                return Wrap(
                  spacing: 4,
                  children: categories.map((category) {
                    return Chip(
                      label: Text(category.name),
                      backgroundColor: Color(
                        int.parse(category.color.substring(1), radix: 16) +
                            0xFF000000,
                      ).withValues(alpha: 0.2),
                    );
                  }).toList(),
                );
              },
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.bookmark, size: 16, color: Colors.orange),
                const SizedBox(width: 4),
                const Text(
                  'Reserved Domain',
                  style: TextStyle(
                    color: Colors.orange,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  'Created: ${DateTime.parse(simpleDomain.createdAt).day}/${DateTime.parse(simpleDomain.createdAt).month}/${DateTime.parse(simpleDomain.createdAt).year}',
                  style: const TextStyle(color: Colors.grey, fontSize: 12),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
