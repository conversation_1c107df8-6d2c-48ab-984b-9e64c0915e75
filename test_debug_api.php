<?php

// Simple test script to debug the categories API
require_once 'vendor/autoload.php';

use App\Models\Category;
use App\Models\Domain;
use App\Models\SimpleDomain;

// Test the domain counting logic directly
echo "=== DEBUGGING CATEGORY DOMAIN COUNTS ===\n\n";

// Get all categories
$categories = Category::all();
echo "Total categories: " . $categories->count() . "\n\n";

// Get total domains
$totalDomains = Domain::count();
$totalSimpleDomains = SimpleDomain::count();
echo "Total domains: $totalDomains\n";
echo "Total simple domains: $totalSimpleDomains\n\n";

// Check sample domains and their categories
echo "=== SAMPLE DOMAINS ===\n";
$sampleDomains = Domain::take(5)->get();
foreach ($sampleDomains as $domain) {
    echo "Domain: {$domain->name}, Categories: " . json_encode($domain->categories) . "\n";
}

echo "\n=== SAMPLE SIMPLE DOMAINS ===\n";
$sampleSimpleDomains = SimpleDomain::take(5)->get();
foreach ($sampleSimpleDomains as $domain) {
    echo "Simple Domain: {$domain->name}, Categories: " . json_encode($domain->categories) . "\n";
}

echo "\n=== CATEGORY COUNTS ===\n";
foreach ($categories as $category) {
    // Test different counting methods
    $count1 = Domain::whereJsonContains('categories', $category->id)->count();
    $count2 = Domain::where('categories', 'like', '%"' . $category->id . '"%')->count();
    $count3 = SimpleDomain::whereJsonContains('categories', $category->id)->count();
    $count4 = SimpleDomain::where('categories', 'like', '%"' . $category->id . '"%')->count();
    
    echo "Category: {$category->name} (ID: {$category->id})\n";
    echo "  Domain counts - JSON: $count1, LIKE: $count2\n";
    echo "  SimpleDomain counts - JSON: $count3, LIKE: $count4\n";
    echo "  Total: " . ($count2 + $count4) . "\n\n";
}

echo "=== DONE ===\n";
