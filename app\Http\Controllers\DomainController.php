<?php

namespace App\Http\Controllers;

use App\Models\Domain;
use App\Models\SimpleDomain;
use App\Models\Category;
use App\Rules\DomainNameRule;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class DomainController extends Controller
{


    public function index(Request $request)
    {
        $query = Domain::query();

        // Apply filters
        if ($request->filled('domain_name')) {
            $query->where('name', 'like', '%' . $request->domain_name . '%');
        }

        if ($request->filled('category_id')) {
            $query->byCategory($request->category_id);
        }

        if ($request->filled('rating')) {
            $query->byRating($request->rating);
        }

        if ($request->filled('expiry_from') && $request->filled('expiry_to')) {
            $query->expiringBetween($request->expiry_from, $request->expiry_to);
        }

        // Apply alphabetical filter
        if ($request->filled('letter')) {
            $letter = strtolower($request->letter);
            $query->where('name', 'like', $letter . '%');
        }

        $domains = $query->orderBy('created_at', 'desc')->paginate(10);
        $categories = Category::all();

        if ($request->ajax()) {
            return response()->json([
                'domains' => $domains->items(),
                'pagination' => [
                    'current_page' => $domains->currentPage(),
                    'last_page' => $domains->lastPage(),
                    'total' => $domains->total()
                ]
            ]);
        }

        return view('domains.index', compact('domains', 'categories'));
    }

    public function create()
    {
        $categories = Category::all();
        return view('domains.create', compact('categories'));
    }

    public function store(Request $request)
    {
        // Clean domain name before validation
        $request->merge([
            'name' => $this->cleanDomainName($request->name)
        ]);

        $request->validate([
            'name' => ['required', 'string', new DomainNameRule, 'unique:domains,name'],
            'extensions' => 'required|array|min:1',
            'categories' => 'required|array|min:1',
            'expiry_date' => 'required|date',
            'rating' => 'required|integer|min:1|max:5'
        ]);

        $domain = Domain::create($request->all());

        // If this domain was purchased from simple domains, delete it from simple_domains table
        if ($request->has('from_simple_domain')) {
            $simpleDomain = SimpleDomain::find($request->from_simple_domain);
            if ($simpleDomain) {
                $simpleDomain->delete();
            }
        }

        if ($request->ajax()) {
            return response()->json(['success' => true, 'domain' => $domain]);
        }

        $successMessage = $request->has('from_simple_domain') 
            ? 'Domain purchased successfully and moved to main domains!' 
            : 'Domain created successfully!';

        return redirect()->route('domains.index')->with('success', $successMessage);
    }

    public function show(Domain $domain)
    {
        return view('domains.show', compact('domain'));
    }

    public function edit(Domain $domain)
    {
        $categories = Category::all();
        return view('domains.edit', compact('domain', 'categories'));
    }

    public function update(Request $request, Domain $domain)
    {
        // Clean domain name before validation
        $request->merge([
            'name' => $this->cleanDomainName($request->name)
        ]);

        $request->validate([
            'name' => ['required', 'string', new DomainNameRule, 'unique:domains,name,' . $domain->id],
            'extensions' => 'required|array|min:1',
            'categories' => 'required|array|min:1',
            'expiry_date' => 'required|date',
            'rating' => 'required|integer|min:1|max:5'
        ]);

        $domain->update($request->all());

        if ($request->ajax()) {
            return response()->json(['success' => true, 'domain' => $domain]);
        }

        return redirect()->route('domains.index')->with('success', 'Domain updated successfully!');
    }

    public function destroy(Domain $domain)
    {
        $domain->delete();

        if (request()->ajax()) {
            return response()->json(['success' => true]);
        }

        return redirect()->route('domains.index')->with('success', 'Domain deleted successfully!');
    }

    public function checkAvailability(Request $request)
    {
        $request->validate([
            'domain' => 'required|string'
        ]);

        // Mock domain availability check - replace with real API
        $isAvailable = rand(0, 1) === 1;
        
        // Update domain if it exists
        $domain = Domain::where('name', $request->domain)->first();
        if ($domain) {
            $domain->update(['is_available' => $isAvailable]);
        }

        return response()->json([
            'available' => $isAvailable,
            'domain' => $request->domain
        ]);
    }

    public function checkDuplicate(Request $request)
    {
        $exists = Domain::where('name', $request->name)->exists();
        return response()->json(['exists' => $exists]);
    }

    /**
     * Clean domain name by removing spaces and converting to lowercase
     */
    private function cleanDomainName($domainName)
    {
        if (!$domainName) {
            return $domainName;
        }
        
        // Remove all spaces and convert to lowercase
        return strtolower(str_replace(' ', '', trim($domainName)));
    }
}
