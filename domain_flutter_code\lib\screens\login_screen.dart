import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../providers/auth_provider.dart';
import '../utils/theme.dart';
import '../utils/network_utils.dart';
import '../utils/error_handler.dart';
import '../utils/debug_utils.dart';

import '../widgets/enhanced_button.dart';
import '../widgets/enhanced_card.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _login() async {
    if (_formKey.currentState!.validate()) {
      // Remove network checking - let the API call handle connectivity issues
      // This prevents false negatives and improves user experience

      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Hide keyboard
      FocusScope.of(context).unfocus();

      final success = await authProvider.login(
        _emailController.text.trim(),
        _passwordController.text,
      );

      if (success && mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('Login successful!'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Navigate to home screen
        Navigator.pushReplacementNamed(context, '/home');
      } else if (mounted) {
        // Show error message
        _showErrorSnackBar(authProvider.error ?? 'Login failed');
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ErrorHandler.showErrorSnackBar(
      context,
      message,
      duration: const Duration(seconds: 5),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(gradient: AppTheme.primaryGradient),
        child: SafeArea(
          child: LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                padding: EdgeInsets.all(
                  constraints.maxHeight > 600
                      ? AppTheme.spaceLG
                      : AppTheme.spaceMD,
                ),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight:
                        constraints.maxHeight -
                        (constraints.maxHeight > 600
                            ? AppTheme.spaceLG * 2
                            : AppTheme.spaceMD * 2),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // App Logo and Title
                      _buildHeader()
                          .animate()
                          .fadeIn(duration: 800.ms)
                          .slideY(begin: -0.3, end: 0),

                      SizedBox(
                        height: constraints.maxHeight > 600
                            ? AppTheme.space2XL
                            : AppTheme.spaceLG,
                      ),

                      // Login Form Card
                      _buildLoginForm()
                          .animate()
                          .fadeIn(duration: 800.ms, delay: 200.ms)
                          .slideY(begin: 0.3, end: 0),

                      // Debug button in debug mode
                      if (kDebugMode) ...[
                        const SizedBox(height: AppTheme.spaceLG),
                        _buildDebugButton(),
                      ],
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            shape: BoxShape.circle,
            boxShadow: AppTheme.floatingShadow,
          ),
          child: const Icon(
            Icons.domain_rounded,
            size: 60,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: AppTheme.spaceLG),
        Text(
          'Domain Manager',
          style: Theme.of(context).textTheme.displayMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w700,
            letterSpacing: -0.5,
          ),
        ),
        const SizedBox(height: AppTheme.spaceSM),
        Text(
          'Manage your domain portfolio with ease',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Colors.white.withValues(alpha: 0.9),
            fontWeight: FontWeight.w400,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLoginForm() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = MediaQuery.of(context).size.height < 600;

        return EnhancedCard.elevated(
          padding: EdgeInsets.all(
            isSmallScreen ? AppTheme.spaceLG : AppTheme.spaceXL,
          ),
          margin: EdgeInsets.zero,
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome Back',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: AppTheme.textPrimaryLight,
                  ),
                ),
                SizedBox(
                  height: isSmallScreen ? AppTheme.spaceXS : AppTheme.spaceSM,
                ),
                Text(
                  'Sign in to your admin account',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryLight,
                  ),
                ),
                SizedBox(
                  height: isSmallScreen ? AppTheme.spaceLG : AppTheme.spaceXL,
                ),

                // Email Field
                _buildEmailField(),
                SizedBox(
                  height: isSmallScreen ? AppTheme.spaceMD : AppTheme.spaceLG,
                ),

                // Password Field
                _buildPasswordField(),
                SizedBox(
                  height: isSmallScreen ? AppTheme.spaceLG : AppTheme.spaceXL,
                ),

                // Login Button
                _buildLoginButton(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmailField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Email Address',
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            color: AppTheme.textPrimaryLight,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppTheme.spaceSM),
        TextFormField(
          controller: _emailController,
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          decoration: InputDecoration(
            hintText: 'Enter your email address',
            prefixIcon: Container(
              margin: const EdgeInsets.all(12),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.primaryLight.withValues(alpha: 0.1),
                borderRadius: AppTheme.smallRadius,
              ),
              child: const Icon(
                Icons.email_outlined,
                size: 20,
                color: AppTheme.primaryLight,
              ),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your email address';
            }
            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
              return 'Please enter a valid email address';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildPasswordField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Password',
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            color: AppTheme.textPrimaryLight,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppTheme.spaceSM),
        TextFormField(
          controller: _passwordController,
          obscureText: _obscurePassword,
          textInputAction: TextInputAction.done,
          onFieldSubmitted: (_) => _login(),
          decoration: InputDecoration(
            hintText: 'Enter your password',
            prefixIcon: Container(
              margin: const EdgeInsets.all(12),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.primaryLight.withValues(alpha: 0.1),
                borderRadius: AppTheme.smallRadius,
              ),
              child: const Icon(
                Icons.lock_outline,
                size: 20,
                color: AppTheme.primaryLight,
              ),
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _obscurePassword
                    ? Icons.visibility_outlined
                    : Icons.visibility_off_outlined,
                color: AppTheme.textSecondaryLight,
              ),
              onPressed: () {
                setState(() {
                  _obscurePassword = !_obscurePassword;
                });
              },
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your password';
            }
            if (value.length < 6) {
              return 'Password must be at least 6 characters';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildLoginButton() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return EnhancedButton.primary(
          text: 'Sign In',
          onPressed: authProvider.isLoading ? null : _login,
          isLoading: authProvider.isLoading,
          isFullWidth: true,
          size: ButtonSize.large,
          icon: Icons.login_rounded,
        );
      },
    );
  }

  Widget _buildDebugButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppTheme.spaceLG),
      child: TextButton.icon(
        onPressed: () async {
          // Show loading dialog
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => const AlertDialog(
              content: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(width: 16),
                  Text('Testing network connectivity...'),
                ],
              ),
            ),
          );

          try {
            // Run network debug tests
            await DebugUtils.printNetworkDebugInfo();

            // Test basic connectivity
            final networkUtils = NetworkUtils();
            final hasBasic = await networkUtils.hasBasicConnectivity();

            if (mounted) {
              Navigator.of(context).pop(); // Close loading dialog

              // Show results
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Network Debug Results'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Basic Connectivity: ${hasBasic ? "✅ OK" : "❌ Failed"}',
                      ),
                      const SizedBox(height: 16),
                      const Text('Check console for detailed logs.'),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('OK'),
                    ),
                  ],
                ),
              );
            }
          } catch (e) {
            if (mounted) {
              Navigator.of(context).pop(); // Close loading dialog
              _showErrorSnackBar('Debug test failed: $e');
            }
          }
        },
        icon: const Icon(Icons.bug_report),
        label: const Text('Debug Network'),
        style: TextButton.styleFrom(foregroundColor: Colors.grey[600]),
      ),
    );
  }
}
