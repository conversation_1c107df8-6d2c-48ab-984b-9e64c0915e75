import 'dart:convert';
import 'dart:io';

/// Ultra-simple API service using basic HttpClient
class UltraSimpleApi {
  static const String baseUrl = 'domain.agfgroupindia.com';
  static const String loginPath = '/api/auth/login';

  /// Ultra-simple login using basic HttpClient
  static Future<Map<String, dynamic>> ultraSimpleLogin(
    String email,
    String password,
  ) async {
    HttpClient? client;
    HttpClientRequest? request;
    HttpClientResponse? response;

    try {
      // Create HTTP client with basic settings
      client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 30);
      client.idleTimeout = const Duration(seconds: 30);

      // Create request
      request = await client.postUrl(Uri.https(baseUrl, loginPath));

      // Set headers
      request.headers.set('Content-Type', 'application/json');
      request.headers.set('Accept', 'application/json');
      request.headers.set('User-Agent', 'Domain-CRM-Mobile/1.0');

      // Prepare body
      final body = json.encode({'email': email, 'password': password});

      // Write body and close request
      request.write(body);
      response = await request.close();

      // Read response
      final responseBody = await response.transform(utf8.decoder).join();

      // Parse response
      final data = json.decode(responseBody) as Map<String, dynamic>;

      // Handle different status codes
      if (response.statusCode == 200) {
        return data;
      } else if (response.statusCode == 422) {
        final message = data['message'] ?? 'Invalid credentials';
        throw Exception(message);
      } else if (response.statusCode == 401) {
        throw Exception('Invalid email or password');
      } else if (response.statusCode >= 500) {
        throw Exception('Server error. Please try again later.');
      } else {
        throw Exception('Login failed. Please try again.');
      }
    } catch (e) {
      // Handle specific error types
      if (e is SocketException) {
        throw Exception(
          'Network connection failed. Please check your internet connection.',
        );
      } else if (e is HttpException) {
        throw Exception('HTTP error: ${e.message}');
      } else if (e is FormatException) {
        throw Exception('Invalid server response. Please try again.');
      } else if (e.toString().contains('timeout')) {
        throw Exception('Connection timed out. Please try again.');
      } else if (e is Exception) {
        // Re-throw our custom exceptions
        rethrow;
      } else {
        throw Exception('Login failed: ${e.toString()}');
      }
    } finally {
      // Clean up
      client?.close();
    }
  }
}
