<?php

namespace Database\Seeders;

use App\Models\Domain;
use App\Models\Category;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class DomainSeeder extends Seeder
{
    public function run(): void
    {
        $categories = Category::all();
        $extensions = ['.com', '.net', '.org', '.info', '.online'];
        
        $sampleDomains = [
            'techstartup',
            'digitalagency',
            'webdesign',
            'ecommerce',
            'blogsite',
            'portfolio',
            'consulting',
            'marketing',
            'development',
            'creative'
        ];

        foreach ($sampleDomains as $domainName) {
            Domain::create([
                'name' => $domainName,
                'extensions' => collect($extensions)->random(rand(1, 3))->values()->toArray(),
                'categories' => $categories->random(rand(1, 2))->pluck('id')->toArray(),
                'expiry_date' => Carbon::now()->addDays(rand(30, 365)),
                'rating' => rand(1, 5),
                'is_available' => rand(0, 1) ? true : false,
                'availability_checked_at' => Carbon::now()->subDays(rand(0, 7)),
            ]);
        }
    }
}