<?php

namespace App\Http\Controllers;

use App\Models\Domain;
use App\Models\SimpleDomain;
use App\Models\Category;
use Illuminate\Http\Request;
use Carbon\Carbon;

class DashboardController extends Controller
{


    public function index()
    {
        // Analytics data
        $totalBuyDomains = Domain::count();
        $totalReserveDomains = SimpleDomain::count();
        $totalCategories = Category::count();
        
        // Domain count per category (from both tables)
        $categoryStats = Category::all()->map(function ($category) {
            $buyDomainsCount = Domain::whereJsonContains('categories', $category->id)->count();
            $reserveDomainsCount = SimpleDomain::whereJsonContains('categories', $category->id)->count();
            return [
                'name' => $category->name,
                'color' => $category->color,
                'buy_count' => $buyDomainsCount,
                'reserve_count' => $reserveDomainsCount,
                'total_count' => $buyDomainsCount + $reserveDomainsCount
            ];
        });

        // Expiry alerts (domains expiring in next 30 days)
        $expiringDomains = Domain::where('expiry_date', '<=', Carbon::now()->addDays(30))
                                ->where('expiry_date', '>=', Carbon::now())
                                ->orderBy('expiry_date')
                                ->get();

        // Top rated domains
        $topRatedDomains = Domain::where('rating', '>=', 4)
                                ->orderBy('rating', 'desc')
                                ->orderBy('created_at', 'desc')
                                ->limit(10)
                                ->get();

        // Recent buy domains
        $recentBuyDomains = Domain::orderBy('created_at', 'desc')
                              ->limit(5)
                              ->get();

        // Recent reserve domains
        $recentReserveDomains = SimpleDomain::orderBy('created_at', 'desc')
                              ->limit(5)
                              ->get();

        return view('dashboard.index', compact(
            'totalBuyDomains',
            'totalReserveDomains',
            'totalCategories',
            'categoryStats',
            'expiringDomains',
            'topRatedDomains',
            'recentBuyDomains',
            'recentReserveDomains'
        ));
    }

    public function getAnalytics(Request $request)
    {
        $data = [
            'total_buy_domains' => Domain::count(),
            'total_reserve_domains' => SimpleDomain::count(),
            'total_categories' => Category::count(),
            'expiring_soon' => Domain::where('expiry_date', '<=', Carbon::now()->addDays(30))->count(),
            'available_domains' => Domain::where('is_available', true)->count(),
            'category_distribution' => Category::all()->map(function ($category) {
                $buyDomainsCount = Domain::whereJsonContains('categories', $category->id)->count();
                $reserveDomainsCount = SimpleDomain::whereJsonContains('categories', $category->id)->count();
                return [
                    'name' => $category->name,
                    'color' => $category->color,
                    'buy_count' => $buyDomainsCount,
                    'reserve_count' => $reserveDomainsCount,
                    'total_count' => $buyDomainsCount + $reserveDomainsCount
                ];
            })
        ];

        return response()->json($data);
    }
}
