<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Carbon\Carbon;

class Domain extends Model
{
    protected $fillable = [
        'name',
        'extensions',
        'categories',
        'expiry_date',
        'rating',
        'is_available',
        'availability_checked_at'
    ];

    protected $casts = [
        'extensions' => 'array',
        'categories' => 'array',
        'expiry_date' => 'date',
        'is_available' => 'boolean',
        'availability_checked_at' => 'datetime'
    ];

    // Calculate days left until expiry
    public function getDaysLeftAttribute()
    {
        if (!$this->expiry_date) {
            return null;
        }
        
        $daysLeft = Carbon::now()->diffInDays($this->expiry_date, false);
        return $daysLeft >= 0 ? (int) $daysLeft : 0;
    }

    // Get category names with colors
    public function getCategoryNamesAttribute()
    {
        if (empty($this->categories)) {
            return [];
        }
        
        return Category::whereIn('id', $this->categories)->pluck('name')->toArray();
    }

    // Get categories with full details
    public function getCategoryDetailsAttribute()
    {
        if (empty($this->categories)) {
            return collect();
        }
        
        return Category::whereIn('id', $this->categories)->get();
    }

    // Get expiry status
    public function getExpiryStatusAttribute()
    {
        $daysLeft = $this->days_left;
        
        if ($daysLeft === null) return 'unknown';
        if ($daysLeft <= 7) return 'critical';
        if ($daysLeft <= 30) return 'warning';
        if ($daysLeft <= 90) return 'attention';
        
        return 'safe';
    }

    // Get availability status text
    public function getAvailabilityStatusAttribute()
    {
        if ($this->is_available === null) {
            return 'Unknown';
        }
        
        return $this->is_available ? 'Available' : 'Not Available';
    }

    // Scope for filtering
    public function scopeByCategory($query, $categoryId)
    {
        return $query->whereJsonContains('categories', (int)$categoryId);
    }

    public function scopeByRating($query, $rating)
    {
        return $query->where('rating', $rating);
    }

    public function scopeExpiringBetween($query, $startDate, $endDate)
    {
        return $query->whereBetween('expiry_date', [$startDate, $endDate]);
    }

    public function scopeExpiringSoon($query, $days = 30)
    {
        return $query->where('expiry_date', '<=', Carbon::now()->addDays($days))
                    ->where('expiry_date', '>=', Carbon::now());
    }

    public function scopeTopRated($query, $minRating = 4)
    {
        return $query->where('rating', '>=', $minRating);
    }

    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    public function scopeSearch($query, $term)
    {
        return $query->where('name', 'like', '%' . $term . '%');
    }
}
