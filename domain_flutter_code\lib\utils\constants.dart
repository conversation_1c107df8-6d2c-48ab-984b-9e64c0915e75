class ApiConstants {
  // Production URL - Confirmed working
  static const String baseUrl = 'https://domain.agfgroupindia.com';

  // Alternative URLs to try if main fails:
  // static const String baseUrl = 'https://domain.agfgroupindia.com/api';
  // static const String baseUrl = 'https://domain.agfgroupindia.com/public/api';

  // Development URLs (uncomment for local development):
  // static const String baseUrl = 'http://********:8000/api'; // For Android emulator
  // static const String baseUrl = 'http://127.0.0.1:8000/api'; // For Android emulator alternative
  // static const String baseUrl = 'http://*************:8000/api'; // For physical device (replace with your IP)
  // static const String baseUrl = 'http://localhost:8000/api'; // For iOS simulator

  static const String loginEndpoint = '/api/auth/login';
  static const String logoutEndpoint = '/api/auth/logout';
  static const String userEndpoint = '/api/auth/user';
  static const String domainsEndpoint = '/api/domains';
  static const String simpleDomainsEndpoint = '/api/simple-domains';
  static const String categoriesEndpoint = '/api/categories';
  static const String dashboardStatsEndpoint = '/api/dashboard/stats';
  static const String settingsProfileEndpoint = '/api/settings/profile';
  static const String settingsExpiryEndpoint = '/api/settings/expiry';
  static const String settingsSystemEndpoint = '/api/settings/system';
  static const String settingsTestEmailEndpoint =
      '/api/settings/expiry/test-email';
}

class StorageKeys {
  static const String authToken = 'auth_token';
  static const String userData = 'user_data';
}
