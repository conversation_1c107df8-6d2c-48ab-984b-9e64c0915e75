import 'package:flutter/material.dart';
import 'enhanced_card.dart';

// Legacy AnimatedCard - now uses EnhancedCard internally
class Animated<PERSON><PERSON> extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final VoidCallback? onTap;
  final Duration duration;

  const AnimatedCard({
    super.key,
    required this.child,
    this.margin,
    this.padding,
    this.onTap,
    this.duration = const Duration(milliseconds: 200),
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard.elevated(
      onTap: onTap,
      margin: margin as EdgeInsets? ?? EdgeInsets.zero,
      padding: padding as EdgeInsets?,
      animationDuration: duration,
      child: child,
    );
  }
}
