# Domain CRM - Flutter Frontend Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture](#architecture)
3. [Technology Stack](#technology-stack)
4. [Installation & Setup](#installation--setup)
5. [Project Structure](#project-structure)
6. [State Management](#state-management)
7. [UI/UX Design](#uiux-design)
8. [API Integration](#api-integration)
9. [Navigation](#navigation)
10. [Testing](#testing)
11. [Build & Deployment](#build--deployment)
12. [Troubleshooting](#troubleshooting)
13. [Contributing](#contributing)

---

## 1. Project Overview

### 1.1 Description
Domain CRM Flutter App is a cross-platform mobile application built with Flutter framework. It provides a comprehensive interface for managing domains, categories, and administrative functions through a beautiful and intuitive mobile experience.

### 1.2 Key Features
- **Cross-Platform**: Runs on iOS, Android, Web, Windows, macOS, and Linux
- **Domain Management**: Complete domain CRUD operations with rich UI
- **Category Management**: Organize and manage domain categories
- **Dashboard Analytics**: Visual statistics and insights
- **Authentication**: Secure login/logout with token management
- **Responsive Design**: Adaptive UI for different screen sizes
- **Offline Support**: Local data caching and sync
- **Real-time Updates**: Live data synchronization
- **Modern UI**: Material Design 3 with custom theming

### 1.3 Target Platforms
- **Primary**: Android, iOS
- **Secondary**: Web, Desktop (Windows, macOS, Linux)

### 1.4 User Personas
- **Domain Administrators**: Manage large domain portfolios
- **Business Owners**: Track domain investments and renewals
- **Developers**: Monitor client domains and projects

---

## 2. Architecture

### 2.1 Application Architecture
```
┌─────────────────────────────────────────────────────────┐
│                    Presentation Layer                   │
├─────────────────────────────────────────────────────────┤
│  Screens  │  Widgets  │  Dialogs  │  Bottom Sheets     │
├─────────────────────────────────────────────────────────┤
│                    Business Logic Layer                 │
├─────────────────────────────────────────────────────────┤
│  Providers │  Services │  Models   │  Utils             │
├─────────────────────────────────────────────────────────┤
│                    Data Layer                           │
├─────────────────────────────────────────────────────────┤
│  API Client │ Local Storage │ Cache │ Preferences       │
├─────────────────────────────────────────────────────────┤
│                    Platform Layer                       │
├─────────────────────────────────────────────────────────┤
│  Flutter Framework │ Dart Runtime │ Platform Channels  │
└─────────────────────────────────────────────────────────┘
```

### 2.2 Design Patterns
- **Provider Pattern**: State management with ChangeNotifier
- **Repository Pattern**: Data access abstraction
- **Singleton Pattern**: API client and services
- **Factory Pattern**: Model creation and parsing
- **Observer Pattern**: Real-time updates and notifications

### 2.3 Data Flow
```
User Interaction → Widget → Provider → Service → API Client → Backend
                     ↓         ↓         ↓          ↓
                  UI Update ← State ← Response ← HTTP Response
```

---

## 3. Technology Stack

### 3.1 Core Technologies
- **Framework**: Flutter 3.8.1+
- **Language**: Dart 3.0+
- **State Management**: Provider 6.1.1
- **HTTP Client**: http 1.1.0
- **Local Storage**: shared_preferences 2.2.2
- **Date Handling**: intl 0.19.0

### 3.2 UI/UX Libraries
```yaml
dependencies:
  # Core Flutter
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  
  # State Management
  provider: ^6.1.1
  
  # Network & API
  http: ^1.1.0
  
  # Local Storage
  shared_preferences: ^2.2.2
  
  # Date & Internationalization
  intl: ^0.19.0
  
  # UI Enhancement
  flutter_spinkit: ^5.2.0
  cached_network_image: ^3.4.1
  flutter_staggered_animations: ^1.1.1
  lottie: ^3.3.1
  shimmer: ^3.0.0
  flutter_animate: ^4.5.2
  google_fonts: ^6.2.1
```

### 3.3 Development Tools
```yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.7
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1
```

### 3.4 Platform-Specific Dependencies
- **Android**: Material Design Components
- **iOS**: Cupertino Design System
- **Web**: Flutter Web Renderer
- **Desktop**: Flutter Desktop Embedding

---

## 4. Installation & Setup

### 4.1 Prerequisites
- Flutter SDK 3.8.1 or higher
- Dart SDK 3.0 or higher
- Android Studio / Xcode (for mobile development)
- VS Code with Flutter extension
- Git

### 4.2 Flutter Installation

#### Windows
```bash
# Download Flutter SDK
git clone https://github.com/flutter/flutter.git -b stable
# Add to PATH
set PATH=%PATH%;C:\flutter\bin

# Verify installation
flutter doctor
```

#### macOS
```bash
# Using Homebrew
brew install flutter

# Or download manually
git clone https://github.com/flutter/flutter.git -b stable
export PATH="$PATH:`pwd`/flutter/bin"

# Verify installation
flutter doctor
```

#### Linux
```bash
# Download Flutter
git clone https://github.com/flutter/flutter.git -b stable
export PATH="$PATH:`pwd`/flutter/bin"

# Install dependencies
sudo apt-get install curl git unzip xz-utils zip libglu1-mesa

# Verify installation
flutter doctor
```

### 4.3 Project Setup

#### Step 1: Clone Repository
```bash
git clone <repository-url>
cd domain-crm/domain_flutter_code
```

#### Step 2: Install Dependencies
```bash
flutter pub get
```

#### Step 3: Generate Code (if needed)
```bash
flutter pub run build_runner build
```

#### Step 4: Configure API Endpoint
```dart
// lib/utils/constants.dart
class ApiConstants {
  // Update this to match your backend URL
  static const String baseUrl = 'http://********:8000/api'; // Android Emulator
  // static const String baseUrl = 'http://localhost:8000/api'; // iOS Simulator
  // static const String baseUrl = 'http://*************:8000/api'; // Physical Device
}
```

#### Step 5: Platform-Specific Setup

##### Android Setup
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<uses-permission android:name="android.permission.INTERNET" />
<application
    android:usesCleartextTraffic="true"
    android:label="Domain CRM"
    android:name="${applicationName}"
    android:icon="@mipmap/ic_launcher">
```

##### iOS Setup
```xml
<!-- ios/Runner/Info.plist -->
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>
```

#### Step 6: Run Application
```bash
# List available devices
flutter devices

# Run on specific device
flutter run -d <device-id>

# Run in debug mode
flutter run --debug

# Run in release mode
flutter run --release
```

### 4.4 IDE Configuration

#### VS Code Extensions
- Flutter
- Dart
- Flutter Widget Snippets
- Awesome Flutter Snippets
- Flutter Tree
- Error Lens

#### Android Studio Plugins
- Flutter
- Dart
- Flutter Inspector
- Flutter Intl

---

## 5. Project Structure

### 5.1 Directory Structure
```
lib/
├── main.dart                 # Application entry point
├── models/                   # Data models
│   ├── admin.dart
│   ├── domain.dart
│   ├── category.dart
│   └── dashboard_stats.dart
├── providers/                # State management
│   ├── auth_provider.dart
│   ├── domain_provider.dart
│   ├── category_provider.dart
│   └── settings_provider.dart
├── screens/                  # UI screens
│   ├── splash_screen.dart
│   ├── login_screen.dart
│   ├── home_screen.dart
│   ├── dashboard_screen.dart
│   ├── domains_screen.dart
│   ├── simple_domains_screen.dart
│   ├── categories_screen.dart
│   └── settings_screen.dart
├── services/                 # Business logic
│   ├── api_service.dart
│   ├── auth_service.dart
│   ├── storage_service.dart
│   └── notification_service.dart
├── utils/                    # Utilities
│   ├── constants.dart
│   ├── theme.dart
│   ├── helpers.dart
│   └── validators.dart
├── widgets/                  # Reusable widgets
│   ├── common/
│   │   ├── custom_button.dart
│   │   ├── custom_text_field.dart
│   │   ├── loading_widget.dart
│   │   └── error_widget.dart
│   ├── domain/
│   │   ├── domain_card.dart
│   │   ├── domain_list.dart
│   │   └── domain_form.dart
│   └── category/
│       ├── category_chip.dart
│       └── category_selector.dart
└── generated/                # Generated files
    └── l10n/                 # Localization
```

### 5.2 File Naming Conventions
- **Screens**: `*_screen.dart`
- **Widgets**: `*_widget.dart` or descriptive names
- **Models**: `*.dart` (singular nouns)
- **Providers**: `*_provider.dart`
- **Services**: `*_service.dart`
- **Utils**: `*.dart` (descriptive names)

### 5.3 Code Organization Principles
- **Single Responsibility**: Each file has one primary purpose
- **Separation of Concerns**: UI, business logic, and data are separated
- **Reusability**: Common widgets and utilities are extracted
- **Maintainability**: Clear structure and naming conventions

---

## 6. State Management

### 6.1 Provider Pattern Implementation

#### Provider Setup
```dart
// main.dart
void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => DomainProvider()),
        ChangeNotifierProvider(create: (_) => CategoryProvider()),
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
      ],
      child: MaterialApp(
        title: 'Domain Manager',
        theme: AppTheme.lightTheme,
        home: const SplashScreen(),
      ),
    );
  }
}
```

#### Provider Example
```dart
// providers/auth_provider.dart
class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  
  Admin? _currentUser;
  bool _isLoading = false;
  String? _error;
  
  // Getters
  Admin? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _currentUser != null;
  
  // Login method
  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _clearError();
    
    try {
      final result = await _authService.login(email, password);
      if (result['success']) {
        _currentUser = Admin.fromJson(result['data']['user']);
        await _saveToken(result['data']['token']);
        _setLoading(false);
        return true;
      } else {
        _setError(result['message'] ?? 'Login failed');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }
  
  // Logout method
  Future<void> logout() async {
    await _authService.logout();
    _currentUser = null;
    await _clearToken();
    notifyListeners();
  }
  
  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }
  
  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
```

### 6.2 State Management Best Practices
- **Immutable State**: Always create new instances when updating state
- **Granular Updates**: Only notify listeners when necessary
- **Error Handling**: Proper error state management
- **Loading States**: Clear loading indicators
- **Memory Management**: Dispose providers properly

### 6.3 Consumer Widgets
```dart
// Using Consumer for specific updates
Consumer<AuthProvider>(
  builder: (context, authProvider, child) {
    if (authProvider.isLoading) {
      return const CircularProgressIndicator();
    }
    
    if (authProvider.error != null) {
      return Text('Error: ${authProvider.error}');
    }
    
    return Text('Welcome, ${authProvider.currentUser?.name}');
  },
)

// Using Selector for optimized updates
Selector<DomainProvider, int>(
  selector: (context, provider) => provider.domains.length,
  builder: (context, domainCount, child) {
    return Text('Total Domains: $domainCount');
  },
)
```

---

## 7. UI/UX Design

### 7.1 Design System

#### Color Palette
```dart
// utils/theme.dart
class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF2563EB);
  static const Color primaryLight = Color(0xFF3B82F6);
  static const Color primaryDark = Color(0xFF1D4ED8);
  
  // Secondary Colors
  static const Color secondary = Color(0xFF64748B);
  static const Color secondaryLight = Color(0xFF94A3B8);
  static const Color secondaryDark = Color(0xFF475569);
  
  // Accent Colors
  static const Color accent = Color(0xFF10B981);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);
  static const Color success = Color(0xFF22C55E);
  
  // Neutral Colors
  static const Color background = Color(0xFFF8FAFC);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color onSurface = Color(0xFF1E293B);
  static const Color onBackground = Color(0xFF334155);
}
```

#### Typography
```dart
class AppTextStyles {
  static const TextStyle heading1 = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: AppColors.onSurface,
  );
  
  static const TextStyle heading2 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    color: AppColors.onSurface,
  );
  
  static const TextStyle body1 = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.onSurface,
  );
  
  static const TextStyle caption = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.secondary,
  );
}
```

#### Theme Configuration
```dart
class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.light,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.primary),
        ),
      ),
    );
  }
}
```

### 7.2 Responsive Design

#### Screen Size Breakpoints
```dart
class ScreenSizes {
  static const double mobile = 600;
  static const double tablet = 900;
  static const double desktop = 1200;
}

class ResponsiveWidget extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  
  const ResponsiveWidget({
    Key? key,
    required this.mobile,
    this.tablet,
    this.desktop,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth >= ScreenSizes.desktop && desktop != null) {
      return desktop!;
    } else if (screenWidth >= ScreenSizes.tablet && tablet != null) {
      return tablet!;
    } else {
      return mobile;
    }
  }
}
```

#### Adaptive Layouts
```dart
class AdaptiveLayout extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ResponsiveWidget(
      mobile: _buildMobileLayout(),
      tablet: _buildTabletLayout(),
      desktop: _buildDesktopLayout(),
    );
  }
  
  Widget _buildMobileLayout() {
    return Column(
      children: [
        // Mobile-specific layout
      ],
    );
  }
  
  Widget _buildTabletLayout() {
    return Row(
      children: [
        // Tablet-specific layout
      ],
    );
  }
  
  Widget _buildDesktopLayout() {
    return Row(
      children: [
        // Desktop-specific layout with sidebar
      ],
    );
  }
}
```

### 7.3 Custom Widgets

#### Custom Button
```dart
class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final ButtonType type;
  
  const CustomButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.type = ButtonType.primary,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: _getButtonStyle(),
        child: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : Text(text),
      ),
    );
  }
  
  ButtonStyle _getButtonStyle() {
    switch (type) {
      case ButtonType.primary:
        return ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
        );
      case ButtonType.secondary:
        return ElevatedButton.styleFrom(
          backgroundColor: AppColors.secondary,
        );
      case ButtonType.danger:
        return ElevatedButton.styleFrom(
          backgroundColor: AppColors.error,
        );
    }
  }
}

enum ButtonType { primary, secondary, danger }
```

### 7.4 Animation and Transitions

#### Page Transitions
```dart
class SlidePageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;
  
  SlidePageRoute({required this.child})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.ease;
            
            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );
            
            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
        );
}
```

#### Loading Animations
```dart
class LoadingWidget extends StatelessWidget {
  final String? message;
  
  const LoadingWidget({Key? key, this.message}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SpinKitFadingCircle(
            color: AppColors.primary,
            size: 50.0,
          ),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: AppTextStyles.body1,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}
```

---

## 8. API Integration

### 8.1 API Service Architecture

#### Base API Service
```dart
// services/api_service.dart
class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();
  
  final http.Client _client = http.Client();
  String? _token;
  
  // Set authentication token
  void setToken(String token) {
    _token = token;
  }
  
  // Clear authentication token
  void clearToken() {
    _token = null;
  }
  
  // Get headers with authentication
  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    
    if (_token != null) {
      headers['Authorization'] = 'Bearer $_token';
    }
    
    return headers;
  }
  
  // GET request
  Future<Map<String, dynamic>> get(String endpoint) async {
    try {
      final url = Uri.parse('${ApiConstants.baseUrl}$endpoint');
      final response = await _client.get(url, headers: _headers);
      return _handleResponse(response);
    } catch (e) {
      throw ApiException('Network error: ${e.toString()}');
    }
  }
  
  // POST request
  Future<Map<String, dynamic>> post(
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    try {
      final url = Uri.parse('${ApiConstants.baseUrl}$endpoint');
      final response = await _client.post(
        url,
        headers: _headers,
        body: json.encode(data),
      );
      return _handleResponse(response);
    } catch (e) {
      throw ApiException('Network error: ${e.toString()}');
    }
  }
  
  // PUT request
  Future<Map<String, dynamic>> put(
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    try {
      final url = Uri.parse('${ApiConstants.baseUrl}$endpoint');
      final response = await _client.put(
        url,
        headers: _headers,
        body: json.encode(data),
      );
      return _handleResponse(response);
    } catch (e) {
      throw ApiException('Network error: ${e.toString()}');
    }
  }
  
  // DELETE request
  Future<Map<String, dynamic>> delete(String endpoint) async {
    try {
      final url = Uri.parse('${ApiConstants.baseUrl}$endpoint');
      final response = await _client.delete(url, headers: _headers);
      return _handleResponse(response);
    } catch (e) {
      throw ApiException('Network error: ${e.toString()}');
    }
  }
  
  // Handle HTTP response
  Map<String, dynamic> _handleResponse(http.Response response) {
    final data = json.decode(response.body) as Map<String, dynamic>;
    
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return data;
    } else {
      throw ApiException(
        data['message'] ?? 'Request failed',
        statusCode: response.statusCode,
      );
    }
  }
}

// Custom exception for API errors
class ApiException implements Exception {
  final String message;
  final int? statusCode;

  ApiException(this.message, {this.statusCode});

  @override
  String toString() => 'ApiException: $message';
}
```

### 8.2 Authentication Service

#### Auth Service Implementation
```dart
// services/auth_service.dart
class AuthService {
  final ApiService _apiService = ApiService();
  final StorageService _storageService = StorageService();

  // Login user
  Future<Map<String, dynamic>> login(String email, String password) async {
    final data = {
      'email': email,
      'password': password,
    };

    final response = await _apiService.post(
      ApiConstants.loginEndpoint,
      data,
    );

    if (response['success']) {
      final token = response['data']['token'];
      await _storageService.saveToken(token);
      _apiService.setToken(token);
    }

    return response;
  }

  // Logout user
  Future<void> logout() async {
    try {
      await _apiService.post(ApiConstants.logoutEndpoint, {});
    } catch (e) {
      // Continue with logout even if API call fails
      print('Logout API call failed: $e');
    } finally {
      await _storageService.clearToken();
      _apiService.clearToken();
    }
  }

  // Get current user
  Future<Map<String, dynamic>> getCurrentUser() async {
    return await _apiService.get(ApiConstants.userEndpoint);
  }

  // Check if user is authenticated
  Future<bool> isAuthenticated() async {
    final token = await _storageService.getToken();
    if (token != null) {
      _apiService.setToken(token);
      try {
        await getCurrentUser();
        return true;
      } catch (e) {
        await _storageService.clearToken();
        _apiService.clearToken();
        return false;
      }
    }
    return false;
  }
}
```

### 8.3 Domain Service

#### Domain Service Implementation
```dart
// services/domain_service.dart
class DomainService {
  final ApiService _apiService = ApiService();

  // Get all domains with pagination
  Future<Map<String, dynamic>> getDomains({
    int page = 1,
    int perPage = 15,
    String? search,
    int? categoryId,
    String? status,
  }) async {
    final queryParams = <String, String>{
      'page': page.toString(),
      'per_page': perPage.toString(),
    };

    if (search != null && search.isNotEmpty) {
      queryParams['search'] = search;
    }

    if (categoryId != null) {
      queryParams['category_id'] = categoryId.toString();
    }

    if (status != null && status.isNotEmpty) {
      queryParams['status'] = status;
    }

    final queryString = queryParams.entries
        .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
        .join('&');

    final endpoint = '${ApiConstants.domainsEndpoint}?$queryString';
    return await _apiService.get(endpoint);
  }

  // Create new domain
  Future<Map<String, dynamic>> createDomain(Domain domain) async {
    return await _apiService.post(
      ApiConstants.domainsEndpoint,
      domain.toJson(),
    );
  }

  // Update existing domain
  Future<Map<String, dynamic>> updateDomain(Domain domain) async {
    return await _apiService.put(
      '${ApiConstants.domainsEndpoint}/${domain.id}',
      domain.toJson(),
    );
  }

  // Delete domain
  Future<Map<String, dynamic>> deleteDomain(int id) async {
    return await _apiService.delete(
      '${ApiConstants.domainsEndpoint}/$id',
    );
  }

  // Get dashboard statistics
  Future<Map<String, dynamic>> getDashboardStats() async {
    return await _apiService.get(ApiConstants.dashboardStatsEndpoint);
  }
}
```

### 8.4 Error Handling

#### Global Error Handler
```dart
// utils/error_handler.dart
class ErrorHandler {
  static String getErrorMessage(dynamic error) {
    if (error is ApiException) {
      switch (error.statusCode) {
        case 401:
          return 'Authentication required. Please login again.';
        case 403:
          return 'You don\'t have permission to perform this action.';
        case 404:
          return 'The requested resource was not found.';
        case 422:
          return 'Invalid data provided. Please check your input.';
        case 429:
          return 'Too many requests. Please try again later.';
        case 500:
          return 'Server error. Please try again later.';
        default:
          return error.message;
      }
    } else if (error is SocketException) {
      return 'No internet connection. Please check your network.';
    } else if (error is TimeoutException) {
      return 'Request timeout. Please try again.';
    } else if (error is FormatException) {
      return 'Invalid response format from server.';
    } else {
      return 'An unexpected error occurred: ${error.toString()}';
    }
  }

  static void showErrorSnackBar(BuildContext context, dynamic error) {
    final message = getErrorMessage(error);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
}
```

### 8.5 Offline Support

#### Cache Service
```dart
// services/cache_service.dart
class CacheService {
  static const String _domainsKey = 'cached_domains';
  static const String _categoriesKey = 'cached_categories';
  static const String _dashboardKey = 'cached_dashboard';

  final StorageService _storageService = StorageService();

  // Cache domains
  Future<void> cacheDomains(List<Domain> domains) async {
    final domainsJson = domains.map((d) => d.toJson()).toList();
    await _storageService.saveData(_domainsKey, domainsJson);
  }

  // Get cached domains
  Future<List<Domain>?> getCachedDomains() async {
    final data = await _storageService.getData(_domainsKey);
    if (data != null) {
      final List<dynamic> domainsJson = data;
      return domainsJson.map((json) => Domain.fromJson(json)).toList();
    }
    return null;
  }

  // Cache categories
  Future<void> cacheCategories(List<Category> categories) async {
    final categoriesJson = categories.map((c) => c.toJson()).toList();
    await _storageService.saveData(_categoriesKey, categoriesJson);
  }

  // Get cached categories
  Future<List<Category>?> getCachedCategories() async {
    final data = await _storageService.getData(_categoriesKey);
    if (data != null) {
      final List<dynamic> categoriesJson = data;
      return categoriesJson.map((json) => Category.fromJson(json)).toList();
    }
    return null;
  }

  // Clear all cache
  Future<void> clearCache() async {
    await _storageService.removeData(_domainsKey);
    await _storageService.removeData(_categoriesKey);
    await _storageService.removeData(_dashboardKey);
  }
}
```

---

## 9. Navigation

### 9.1 Navigation Structure

#### App Navigation Flow
```
Splash Screen
     ↓
Login Screen (if not authenticated)
     ↓
Home Screen (Bottom Navigation)
├── Dashboard Tab
├── Domains Tab
│   ├── Domain List
│   ├── Domain Details
│   ├── Add Domain
│   └── Edit Domain
├── Simple Domains Tab
├── Categories Tab
│   ├── Category List
│   ├── Add Category
│   └── Edit Category
└── Settings Tab
    ├── Profile Settings
    ├── Notification Settings
    └── About
```

#### Route Configuration
```dart
// utils/routes.dart
class AppRoutes {
  static const String splash = '/';
  static const String login = '/login';
  static const String home = '/home';
  static const String domainDetails = '/domain-details';
  static const String addDomain = '/add-domain';
  static const String editDomain = '/edit-domain';
  static const String addCategory = '/add-category';
  static const String editCategory = '/edit-category';
  static const String settings = '/settings';
  static const String profile = '/profile';
}

class AppRouter {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AppRoutes.splash:
        return MaterialPageRoute(builder: (_) => const SplashScreen());

      case AppRoutes.login:
        return SlidePageRoute(child: const LoginScreen());

      case AppRoutes.home:
        return MaterialPageRoute(builder: (_) => const HomeScreen());

      case AppRoutes.domainDetails:
        final domain = settings.arguments as Domain;
        return SlidePageRoute(child: DomainDetailsScreen(domain: domain));

      case AppRoutes.addDomain:
        return SlidePageRoute(child: const AddDomainScreen());

      case AppRoutes.editDomain:
        final domain = settings.arguments as Domain;
        return SlidePageRoute(child: EditDomainScreen(domain: domain));

      default:
        return MaterialPageRoute(
          builder: (_) => const Scaffold(
            body: Center(child: Text('Page not found')),
          ),
        );
    }
  }
}
```

### 9.2 Bottom Navigation

#### Bottom Navigation Implementation
```dart
// screens/home_screen.dart
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  late PageController _pageController;

  final List<Widget> _screens = [
    const DashboardScreen(),
    const DomainsScreen(),
    const SimpleDomainsScreen(),
    const CategoriesScreen(),
    const SettingsScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        controller: _pageController,
        onPageChanged: _onPageChanged,
        children: _screens,
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: _onNavigationTap,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.secondary,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.domain),
            label: 'Domains',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_cart),
            label: 'Marketplace',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.category),
            label: 'Categories',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Settings',
          ),
        ],
      ),
    );
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
    HapticFeedback.lightImpact();
  }

  void _onNavigationTap(int index) {
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }
}
```

### 9.3 Deep Linking

#### Deep Link Configuration
```dart
// main.dart
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Domain Manager',
      theme: AppTheme.lightTheme,
      initialRoute: AppRoutes.splash,
      onGenerateRoute: AppRouter.generateRoute,
      // Deep linking support
      onGenerateInitialRoutes: (String initialRoute) {
        return [AppRouter.generateRoute(RouteSettings(name: initialRoute))];
      },
    );
  }
}
```

---

## 10. Testing

### 10.1 Testing Strategy

#### Test Pyramid
```
┌─────────────────────────────────┐
│         E2E Tests (10%)         │  ← Integration tests
├─────────────────────────────────┤
│      Widget Tests (20%)         │  ← UI component tests
├─────────────────────────────────┤
│       Unit Tests (70%)          │  ← Business logic tests
└─────────────────────────────────┘
```

### 10.2 Unit Testing

#### Model Tests
```dart
// test/models/domain_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:domain_flutter_code/models/domain.dart';

void main() {
  group('Domain Model Tests', () {
    test('should create Domain from JSON', () {
      // Arrange
      final json = {
        'id': 1,
        'name': 'example.com',
        'category_id': 1,
        'price': '99.99',
        'status': 'active',
        'expiry_date': '2024-12-31',
      };

      // Act
      final domain = Domain.fromJson(json);

      // Assert
      expect(domain.id, 1);
      expect(domain.name, 'example.com');
      expect(domain.categoryId, 1);
      expect(domain.price, 99.99);
      expect(domain.status, DomainStatus.active);
    });

    test('should convert Domain to JSON', () {
      // Arrange
      final domain = Domain(
        id: 1,
        name: 'example.com',
        categoryId: 1,
        price: 99.99,
        status: DomainStatus.active,
        expiryDate: DateTime(2024, 12, 31),
      );

      // Act
      final json = domain.toJson();

      // Assert
      expect(json['id'], 1);
      expect(json['name'], 'example.com');
      expect(json['category_id'], 1);
      expect(json['price'], '99.99');
      expect(json['status'], 'active');
    });

    test('should validate domain name', () {
      // Arrange & Act & Assert
      expect(Domain.isValidDomainName('example.com'), true);
      expect(Domain.isValidDomainName('sub.example.com'), true);
      expect(Domain.isValidDomainName('invalid domain'), false);
      expect(Domain.isValidDomainName(''), false);
    });
  });
}
```

#### Provider Tests
```dart
// test/providers/auth_provider_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:domain_flutter_code/providers/auth_provider.dart';
import 'package:domain_flutter_code/services/auth_service.dart';

@GenerateMocks([AuthService])
import 'auth_provider_test.mocks.dart';

void main() {
  group('AuthProvider Tests', () {
    late AuthProvider authProvider;
    late MockAuthService mockAuthService;

    setUp(() {
      mockAuthService = MockAuthService();
      authProvider = AuthProvider();
      // Inject mock service
      authProvider.authService = mockAuthService;
    });

    test('should login successfully', () async {
      // Arrange
      final loginResponse = {
        'success': true,
        'data': {
          'user': {
            'id': 1,
            'name': 'Test User',
            'email': '<EMAIL>',
            'role': 'admin',
          },
          'token': 'test-token',
        },
      };

      when(mockAuthService.login('<EMAIL>', 'password'))
          .thenAnswer((_) async => loginResponse);

      // Act
      final result = await authProvider.login('<EMAIL>', 'password');

      // Assert
      expect(result, true);
      expect(authProvider.isAuthenticated, true);
      expect(authProvider.currentUser?.email, '<EMAIL>');
      expect(authProvider.error, null);
    });

    test('should handle login failure', () async {
      // Arrange
      when(mockAuthService.login('<EMAIL>', 'wrong-password'))
          .thenThrow(Exception('Invalid credentials'));

      // Act
      final result = await authProvider.login('<EMAIL>', 'wrong-password');

      // Assert
      expect(result, false);
      expect(authProvider.isAuthenticated, false);
      expect(authProvider.error, isNotNull);
    });

    test('should logout successfully', () async {
      // Arrange
      authProvider.setCurrentUser(Admin(
        id: 1,
        name: 'Test User',
        email: '<EMAIL>',
        role: 'admin',
      ));

      when(mockAuthService.logout()).thenAnswer((_) async {});

      // Act
      await authProvider.logout();

      // Assert
      expect(authProvider.isAuthenticated, false);
      expect(authProvider.currentUser, null);
    });
  });
}
```

### 10.3 Widget Testing

#### Screen Tests
```dart
// test/screens/login_screen_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:mockito/mockito.dart';
import 'package:domain_flutter_code/screens/login_screen.dart';
import 'package:domain_flutter_code/providers/auth_provider.dart';

import '../providers/auth_provider_test.mocks.dart';

void main() {
  group('LoginScreen Widget Tests', () {
    late MockAuthProvider mockAuthProvider;

    setUp(() {
      mockAuthProvider = MockAuthProvider();
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: ChangeNotifierProvider<AuthProvider>.value(
          value: mockAuthProvider,
          child: const LoginScreen(),
        ),
      );
    }

    testWidgets('should display login form', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.isLoading).thenReturn(false);
      when(mockAuthProvider.error).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Login'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(2));
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('should show loading indicator when logging in', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.isLoading).thenReturn(true);
      when(mockAuthProvider.error).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should show error message when login fails', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.isLoading).thenReturn(false);
      when(mockAuthProvider.error).thenReturn('Invalid credentials');

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Invalid credentials'), findsOneWidget);
    });

    testWidgets('should call login when form is submitted', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.isLoading).thenReturn(false);
      when(mockAuthProvider.error).thenReturn(null);
      when(mockAuthProvider.login(any, any)).thenAnswer((_) async => true);

      await tester.pumpWidget(createTestWidget());

      // Act
      await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).last, 'password');
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      // Assert
      verify(mockAuthProvider.login('<EMAIL>', 'password')).called(1);
    });
  });
}
```

### 10.4 Integration Testing

#### E2E Tests
```dart
// integration_test/app_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:domain_flutter_code/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('App Integration Tests', () {
    testWidgets('complete login flow', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Should show splash screen first
      expect(find.text('Domain Manager'), findsOneWidget);

      // Wait for navigation to login screen
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Should show login screen
      expect(find.text('Login'), findsOneWidget);

      // Enter credentials
      await tester.enterText(
        find.byKey(const Key('email_field')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.byKey(const Key('password_field')),
        'password',
      );

      // Tap login button
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pumpAndSettle();

      // Should navigate to home screen
      expect(find.text('Dashboard'), findsOneWidget);
      expect(find.byType(BottomNavigationBar), findsOneWidget);
    });

    testWidgets('domain management flow', (WidgetTester tester) async {
      // Assume user is already logged in
      app.main();
      await tester.pumpAndSettle();

      // Navigate to domains tab
      await tester.tap(find.text('Domains'));
      await tester.pumpAndSettle();

      // Should show domains list
      expect(find.byType(ListView), findsOneWidget);

      // Tap add domain button
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // Should show add domain form
      expect(find.text('Add Domain'), findsOneWidget);

      // Fill form
      await tester.enterText(
        find.byKey(const Key('domain_name_field')),
        'test-domain.com',
      );
      await tester.enterText(
        find.byKey(const Key('domain_price_field')),
        '99.99',
      );

      // Submit form
      await tester.tap(find.byKey(const Key('save_button')));
      await tester.pumpAndSettle();

      // Should navigate back to domains list
      expect(find.text('test-domain.com'), findsOneWidget);
    });
  });
}
```

### 10.5 Test Configuration

#### Test Setup
```dart
// test/test_helpers.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:domain_flutter_code/providers/auth_provider.dart';
import 'package:domain_flutter_code/providers/domain_provider.dart';
import 'package:domain_flutter_code/providers/category_provider.dart';

class TestHelpers {
  static Widget createTestApp({
    required Widget child,
    AuthProvider? authProvider,
    DomainProvider? domainProvider,
    CategoryProvider? categoryProvider,
  }) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<AuthProvider>.value(
          value: authProvider ?? AuthProvider(),
        ),
        ChangeNotifierProvider<DomainProvider>.value(
          value: domainProvider ?? DomainProvider(),
        ),
        ChangeNotifierProvider<CategoryProvider>.value(
          value: categoryProvider ?? CategoryProvider(),
        ),
      ],
      child: MaterialApp(
        home: child,
      ),
    );
  }

  static Future<void> pumpAndSettle(WidgetTester tester) async {
    await tester.pumpAndSettle(const Duration(seconds: 1));
  }
}
```

#### Running Tests
```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/models/domain_test.dart

# Run tests with coverage
flutter test --coverage

# Run integration tests
flutter test integration_test/

# Run tests on specific device
flutter test -d chrome
flutter test -d android
```

---

## 11. Build & Deployment

### 11.1 Build Configuration

#### Android Build
```bash
# Debug build
flutter build apk --debug

# Release build
flutter build apk --release

# Split APKs by ABI
flutter build apk --split-per-abi

# App Bundle (recommended for Play Store)
flutter build appbundle --release
```

#### iOS Build
```bash
# Debug build
flutter build ios --debug

# Release build
flutter build ios --release

# Archive for App Store
flutter build ipa --release
```

#### Web Build
```bash
# Debug build
flutter build web --debug

# Release build
flutter build web --release

# Build with specific renderer
flutter build web --web-renderer canvaskit
flutter build web --web-renderer html
```

#### Desktop Builds
```bash
# Windows
flutter build windows --release

# macOS
flutter build macos --release

# Linux
flutter build linux --release
```

### 11.2 Build Configuration Files

#### Android Configuration
```gradle
// android/app/build.gradle
android {
    compileSdkVersion 34
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        applicationId "com.example.domain_flutter_code"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}
```

#### iOS Configuration
```xml
<!-- ios/Runner/Info.plist -->
<key>CFBundleDisplayName</key>
<string>Domain Manager</string>
<key>CFBundleIdentifier</key>
<string>com.example.domainFlutterCode</string>
<key>CFBundleVersion</key>
<string>$(FLUTTER_BUILD_NUMBER)</string>
<key>CFBundleShortVersionString</key>
<string>$(FLUTTER_BUILD_NAME)</string>
```

### 11.3 Environment Configuration

#### Environment Variables
```dart
// lib/config/environment.dart
enum Environment { development, staging, production }

class EnvironmentConfig {
  static const Environment _environment = Environment.development;

  static Environment get environment => _environment;

  static String get apiBaseUrl {
    switch (_environment) {
      case Environment.development:
        return 'http://localhost:8000/api';
      case Environment.staging:
        return 'https://staging-api.domain-crm.com/api';
      case Environment.production:
        return 'https://api.domain-crm.com/api';
    }
  }

  static bool get isDebug => _environment == Environment.development;
  static bool get isProduction => _environment == Environment.production;
}
```

#### Build Flavors
```yaml
# pubspec.yaml
flutter:
  assets:
    - assets/config/dev.json
    - assets/config/staging.json
    - assets/config/prod.json
```

### 11.4 Code Signing

#### Android Signing
```bash
# Generate keystore
keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload

# Create key.properties
storePassword=<password>
keyPassword=<password>
keyAlias=upload
storeFile=<location of the key store file>
```

#### iOS Signing
- Configure signing in Xcode
- Use automatic signing for development
- Use manual signing for distribution
- Configure provisioning profiles

### 11.5 Continuous Integration

#### GitHub Actions
```yaml
# .github/workflows/flutter.yml
name: Flutter CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.8.1'

    - name: Install dependencies
      run: flutter pub get

    - name: Run tests
      run: flutter test --coverage

    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: coverage/lcov.info

  build-android:
    needs: test
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.8.1'

    - name: Build APK
      run: flutter build apk --release

    - name: Upload APK
      uses: actions/upload-artifact@v3
      with:
        name: app-release.apk
        path: build/app/outputs/flutter-apk/app-release.apk

  build-ios:
    needs: test
    runs-on: macos-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.8.1'

    - name: Build iOS
      run: flutter build ios --release --no-codesign
```

### 11.6 App Store Deployment

#### Google Play Store
1. Create app bundle: `flutter build appbundle --release`
2. Upload to Google Play Console
3. Configure store listing
4. Set up release management
5. Submit for review

#### Apple App Store
1. Build IPA: `flutter build ipa --release`
2. Upload to App Store Connect
3. Configure app metadata
4. Submit for review
5. Manage releases

#### Web Deployment
```bash
# Build for web
flutter build web --release

# Deploy to Firebase Hosting
firebase deploy --only hosting

# Deploy to GitHub Pages
# Copy build/web contents to gh-pages branch
```

---

## 12. Troubleshooting

### 12.1 Common Issues

#### Build Issues

**Gradle Build Failure**
```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter build apk

# Update Gradle wrapper
cd android
./gradlew wrapper --gradle-version 7.6
```

**iOS Build Failure**
```bash
# Clean iOS build
flutter clean
cd ios
rm -rf Pods
rm Podfile.lock
pod install
cd ..
flutter build ios
```

**Web Build Issues**
```bash
# Clear web cache
flutter clean
flutter pub get
flutter build web --web-renderer canvaskit
```

#### Runtime Issues

**Network Connection Error**
```dart
// Check network connectivity
import 'package:connectivity_plus/connectivity_plus.dart';

Future<bool> checkConnectivity() async {
  final connectivityResult = await Connectivity().checkConnectivity();
  return connectivityResult != ConnectivityResult.none;
}
```

**State Management Issues**
```dart
// Debug provider state
Consumer<AuthProvider>(
  builder: (context, authProvider, child) {
    print('Auth state: ${authProvider.isAuthenticated}');
    print('Current user: ${authProvider.currentUser}');
    print('Error: ${authProvider.error}');
    return YourWidget();
  },
)
```

**Memory Leaks**
```dart
// Proper disposal
@override
void dispose() {
  _controller.dispose();
  _subscription?.cancel();
  super.dispose();
}
```

### 12.2 Debugging Tools

#### Flutter Inspector
- Use Flutter Inspector in IDE
- Analyze widget tree
- Check widget properties
- Debug layout issues

#### Performance Profiling
```bash
# Profile app performance
flutter run --profile

# Analyze performance
flutter run --trace-startup --profile
```

#### Logging
```dart
// Custom logger
import 'dart:developer' as developer;

class Logger {
  static void debug(String message) {
    developer.log(message, name: 'DEBUG');
  }

  static void error(String message, [dynamic error]) {
    developer.log(message, name: 'ERROR', error: error);
  }

  static void info(String message) {
    developer.log(message, name: 'INFO');
  }
}
```

### 12.3 Performance Optimization

#### Image Optimization
```dart
// Use cached network images
CachedNetworkImage(
  imageUrl: imageUrl,
  placeholder: (context, url) => const CircularProgressIndicator(),
  errorWidget: (context, url, error) => const Icon(Icons.error),
  memCacheWidth: 300,
  memCacheHeight: 300,
)
```

#### List Performance
```dart
// Use ListView.builder for large lists
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) {
    return ListTile(
      title: Text(items[index].title),
    );
  },
)

// Use AutomaticKeepAliveClientMixin for complex widgets
class ExpensiveWidget extends StatefulWidget {
  @override
  _ExpensiveWidgetState createState() => _ExpensiveWidgetState();
}

class _ExpensiveWidgetState extends State<ExpensiveWidget>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return YourExpensiveWidget();
  }
}
```

#### Memory Management
```dart
// Dispose resources properly
class MyWidget extends StatefulWidget {
  @override
  _MyWidgetState createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  late StreamSubscription _subscription;
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this);
    _subscription = stream.listen((data) {
      // Handle data
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _subscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container();
  }
}
```

---

## 13. Contributing

### 13.1 Development Guidelines

#### Code Style
- Follow Dart style guide
- Use meaningful variable names
- Write self-documenting code
- Add comments for complex logic
- Use consistent formatting

#### Git Workflow
```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes and commit
git add .
git commit -m "feat: add new feature"

# Push and create PR
git push origin feature/new-feature
```

#### Commit Message Format
```
type(scope): description

Types:
- feat: new feature
- fix: bug fix
- docs: documentation
- style: formatting
- refactor: code refactoring
- test: adding tests
- chore: maintenance
```

### 13.2 Pull Request Process

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/feature-name
   ```

2. **Make Changes**
   - Write clean, tested code
   - Follow coding standards
   - Update documentation

3. **Test Changes**
   ```bash
   flutter test
   flutter analyze
   ```

4. **Submit Pull Request**
   - Clear description
   - Link related issues
   - Request code review

5. **Code Review**
   - Address feedback
   - Update code as needed
   - Ensure CI passes

### 13.3 Release Process

#### Version Management
```yaml
# pubspec.yaml
version: 1.2.3+4
#        │ │ │  │
#        │ │ │  └── Build number
#        │ │ └───── Patch version
#        │ └─────── Minor version
#        └───────── Major version
```

#### Release Checklist
- [ ] Update version number
- [ ] Update changelog
- [ ] Run full test suite
- [ ] Build for all platforms
- [ ] Test on real devices
- [ ] Create release notes
- [ ] Tag release in Git
- [ ] Deploy to app stores

---

## Appendices

### A. Widget Catalog
### B. API Reference
### C. Design Guidelines
### D. Performance Benchmarks
### E. Changelog

---

**Document Version**: 1.0
**Last Updated**: 2024-01-31
**Author**: Flutter Development Team
**Contact**: <EMAIL>
```
