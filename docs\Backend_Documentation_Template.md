# Domain CRM - Backend Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture](#architecture)
3. [Technology Stack](#technology-stack)
4. [Installation & Setup](#installation--setup)
5. [Database Design](#database-design)
6. [API Documentation](#api-documentation)
7. [Authentication & Security](#authentication--security)
8. [Configuration](#configuration)
9. [Testing](#testing)
10. [Deployment](#deployment)
11. [Troubleshooting](#troubleshooting)
12. [Contributing](#contributing)

---

## 1. Project Overview

### 1.1 Description
Domain CRM is a comprehensive domain management system built with Laravel framework. It provides a robust backend API for managing domains, categories, user authentication, and administrative functions.

### 1.2 Key Features
- **Domain Management**: Complete CRUD operations for domains
- **Category Management**: Organize domains by categories
- **User Authentication**: Secure login/logout with Laravel Sanctum
- **Admin Panel**: Web-based administration interface
- **Dashboard Analytics**: Real-time statistics and insights
- **Email Notifications**: Automated domain expiry alerts
- **API-First Design**: RESTful API for mobile and web clients

### 1.3 Business Logic
- Multi-tenant domain management
- Role-based access control (Admin, Super Admin)
- Domain expiry tracking and notifications
- Simple domain marketplace functionality

---

## 2. Architecture

### 2.1 System Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │    │   Web Admin     │    │   Third Party   │
│   (Mobile)      │    │   Panel         │    │   Services      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Laravel API   │
                    │   (Backend)     │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   SQLite/MySQL  │
                    │   Database      │
                    └─────────────────┘
```

### 2.2 MVC Pattern
- **Models**: Eloquent ORM models for data representation
- **Views**: Blade templates for admin panel
- **Controllers**: API and web controllers for business logic

### 2.3 Directory Structure
```
app/
├── Console/           # Artisan commands
├── Http/
│   ├── Controllers/   # API and web controllers
│   ├── Middleware/    # Custom middleware
│   └── Requests/      # Form request validation
├── Mail/              # Email templates
├── Models/            # Eloquent models
├── Providers/         # Service providers
└── Rules/             # Custom validation rules

config/                # Configuration files
database/
├── factories/         # Model factories
├── migrations/        # Database migrations
└── seeders/          # Database seeders

resources/
├── css/              # Stylesheets
├── js/               # JavaScript files
└── views/            # Blade templates

routes/
├── api.php           # API routes
├── web.php           # Web routes
└── console.php       # Console routes
```

---

## 3. Technology Stack

### 3.1 Core Technologies
- **Framework**: Laravel 12.x
- **PHP Version**: 8.2+
- **Database**: SQLite (development), MySQL (production)
- **Authentication**: Laravel Sanctum
- **Frontend**: Blade Templates + Bootstrap 5
- **Build Tool**: Vite
- **CSS Framework**: Tailwind CSS

### 3.2 Dependencies
```json
{
  "require": {
    "php": "^8.2",
    "laravel/framework": "^12.0",
    "laravel/sanctum": "^4.2",
    "laravel/tinker": "^2.10.1"
  },
  "require-dev": {
    "fakerphp/faker": "^1.23",
    "laravel/pail": "^1.2.2",
    "laravel/pint": "^1.13",
    "laravel/sail": "^1.41",
    "mockery/mockery": "^1.6",
    "nunomaduro/collision": "^8.6",
    "phpunit/phpunit": "^11.5.3"
  }
}
```

### 3.3 Development Tools
- **Code Style**: Laravel Pint
- **Testing**: PHPUnit
- **Debugging**: Laravel Pail
- **Package Manager**: Composer
- **Version Control**: Git

---

## 4. Installation & Setup

### 4.1 Prerequisites
- PHP 8.2 or higher
- Composer
- Node.js & npm
- SQLite or MySQL
- Git

### 4.2 Installation Steps

#### Step 1: Clone Repository
```bash
git clone <repository-url>
cd domain-crm
```

#### Step 2: Install Dependencies
```bash
composer install
npm install
```

#### Step 3: Environment Configuration
```bash
cp .env.example .env
php artisan key:generate
```

#### Step 4: Database Setup
```bash
# For SQLite (development)
touch database/database.sqlite

# Run migrations
php artisan migrate

# Seed database
php artisan db:seed
```

#### Step 5: Laravel Sanctum Setup
```bash
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
php artisan migrate
```

#### Step 6: Create Admin User
```bash
php artisan tinker
```
```php
App\Models\Admin::create([
    'name' => 'Admin User',
    'email' => '<EMAIL>',
    'password' => bcrypt('password'),
    'role' => 'super_admin',
    'is_active' => true
]);
```

#### Step 7: Start Development Server
```bash
php artisan serve
npm run dev
```

### 4.3 Verification
- Backend API: http://localhost:8000/api
- Admin Panel: http://localhost:8000/admin
- API Documentation: http://localhost:8000/api/docs

---

## 5. Database Design

### 5.1 Entity Relationship Diagram
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Admins    │    │  Domains    │    │ Categories  │
├─────────────┤    ├─────────────┤    ├─────────────┤
│ id          │    │ id          │    │ id          │
│ name        │    │ name        │    │ name        │
│ email       │    │ category_id │────│ description │
│ password    │    │ price       │    │ created_at  │
│ role        │    │ status      │    │ updated_at  │
│ is_active   │    │ expiry_date │    └─────────────┘
│ created_at  │    │ created_at  │
│ updated_at  │    │ updated_at  │
└─────────────┘    └─────────────┘
```

### 5.2 Table Specifications

#### Admins Table
```sql
CREATE TABLE admins (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'super_admin') DEFAULT 'admin',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

#### Domains Table
```sql
CREATE TABLE domains (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    category_id BIGINT UNSIGNED,
    price DECIMAL(10,2),
    status ENUM('active', 'expired', 'pending') DEFAULT 'active',
    expiry_date DATE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    FOREIGN KEY (category_id) REFERENCES categories(id)
);
```

#### Categories Table
```sql
CREATE TABLE categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

### 5.3 Indexes
- Primary keys on all tables
- Unique index on admins.email
- Index on domains.category_id
- Index on domains.status
- Index on domains.expiry_date

---

## 6. API Documentation

### 6.1 Base URL
```
Development: http://localhost:8000/api
Production: https://your-domain.com/api
```

### 6.2 Authentication Endpoints

#### POST /auth/login
**Description**: Authenticate admin user
**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "password"
}
```
**Response**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "name": "Admin User",
      "email": "<EMAIL>",
      "role": "super_admin"
    },
    "token": "1|abc123..."
  }
}
```

#### POST /auth/logout
**Description**: Logout current user
**Headers**: `Authorization: Bearer {token}`
**Response**:
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

#### GET /auth/user
**Description**: Get current authenticated user
**Headers**: `Authorization: Bearer {token}`
**Response**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Admin User",
    "email": "<EMAIL>",
    "role": "super_admin"
  }
}
```

### 6.3 Domain Endpoints

#### GET /domains
**Description**: List all domains with pagination
**Headers**: `Authorization: Bearer {token}`
**Query Parameters**:
- `page`: Page number (default: 1)
- `per_page`: Items per page (default: 15)
- `search`: Search term
- `category_id`: Filter by category
- `status`: Filter by status

**Response**:
```json
{
  "success": true,
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "name": "example.com",
        "category": {
          "id": 1,
          "name": "Business"
        },
        "price": "99.99",
        "status": "active",
        "expiry_date": "2024-12-31"
      }
    ],
    "total": 100,
    "per_page": 15
  }
}
```

#### POST /domains
**Description**: Create new domain
**Headers**: `Authorization: Bearer {token}`
**Request Body**:
```json
{
  "name": "newdomain.com",
  "category_id": 1,
  "price": 149.99,
  "status": "active",
  "expiry_date": "2025-12-31"
}
```

#### PUT /domains/{id}
**Description**: Update existing domain
**Headers**: `Authorization: Bearer {token}`
**Request Body**: Same as POST

#### DELETE /domains/{id}
**Description**: Delete domain
**Headers**: `Authorization: Bearer {token}`

### 6.4 Category Endpoints

#### GET /categories
**Description**: List all categories
**Headers**: `Authorization: Bearer {token}`

#### POST /categories
**Description**: Create new category
**Headers**: `Authorization: Bearer {token}`
**Request Body**:
```json
{
  "name": "Technology",
  "description": "Tech-related domains"
}
```

### 6.5 Dashboard Endpoints

#### GET /dashboard/stats
**Description**: Get dashboard statistics
**Headers**: `Authorization: Bearer {token}`
**Response**:
```json
{
  "success": true,
  "data": {
    "total_domains": 150,
    "active_domains": 120,
    "expired_domains": 20,
    "expiring_soon": 10,
    "total_categories": 8,
    "recent_domains": []
  }
}
```

---

## 7. Authentication & Security

### 7.1 Laravel Sanctum
- Token-based authentication
- SPA authentication support
- Mobile app authentication
- Token expiration and refresh

### 7.2 Security Features
- CSRF protection
- SQL injection prevention
- XSS protection
- Rate limiting
- Input validation
- Password hashing (bcrypt)

### 7.3 Middleware
- Authentication middleware
- CORS middleware
- Rate limiting middleware
- Admin role middleware

### 7.4 Configuration
```php
// config/sanctum.php
'stateful' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', sprintf(
    '%s%s',
    'localhost,localhost:3000,127.0.0.1,127.0.0.1:8000,::1',
    Sanctum::currentApplicationUrlWithPort()
))),

'expiration' => null, // Token never expires
```

---

## 8. Configuration

### 8.1 Environment Variables
```env
APP_NAME="Domain CRM"
APP_ENV=local
APP_KEY=base64:...
APP_DEBUG=true
APP_URL=http://localhost:8000

DB_CONNECTION=sqlite
DB_DATABASE=/absolute/path/to/database.sqlite

MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

SANCTUM_STATEFUL_DOMAINS=localhost,127.0.0.1
```

### 8.2 Cache Configuration
```php
// config/cache.php
'default' => env('CACHE_DRIVER', 'file'),
'stores' => [
    'file' => [
        'driver' => 'file',
        'path' => storage_path('framework/cache/data'),
    ],
],
```

### 8.3 Queue Configuration
```php
// config/queue.php
'default' => env('QUEUE_CONNECTION', 'sync'),
'connections' => [
    'sync' => [
        'driver' => 'sync',
    ],
],
```

---

## 9. Testing

### 9.1 Test Structure
```
tests/
├── Feature/           # Integration tests
│   ├── AuthTest.php
│   ├── DomainTest.php
│   └── CategoryTest.php
└── Unit/              # Unit tests
    ├── ModelTest.php
    └── ServiceTest.php
```

### 9.2 Running Tests
```bash
# Run all tests
php artisan test

# Run specific test file
php artisan test tests/Feature/AuthTest.php

# Run with coverage
php artisan test --coverage
```

### 9.3 Test Examples
```php
// tests/Feature/AuthTest.php
public function test_admin_can_login()
{
    $admin = Admin::factory()->create();
    
    $response = $this->postJson('/api/auth/login', [
        'email' => $admin->email,
        'password' => 'password',
    ]);
    
    $response->assertStatus(200)
             ->assertJsonStructure([
                 'success',
                 'data' => ['user', 'token']
             ]);
}
```

---

## 10. Deployment

### 10.1 Production Checklist
- [ ] Set APP_ENV=production
- [ ] Set APP_DEBUG=false
- [ ] Configure production database
- [ ] Set up SSL certificate
- [ ] Configure mail settings
- [ ] Set up backup strategy
- [ ] Configure monitoring
- [ ] Set up logging

### 10.2 Server Requirements
- PHP 8.2+
- Composer
- Web server (Apache/Nginx)
- MySQL/PostgreSQL
- SSL certificate
- Cron jobs for scheduled tasks

### 10.3 Deployment Steps
```bash
# 1. Clone repository
git clone <repo-url> /var/www/domain-crm

# 2. Install dependencies
composer install --optimize-autoloader --no-dev

# 3. Set permissions
chmod -R 755 /var/www/domain-crm
chmod -R 775 storage bootstrap/cache

# 4. Configure environment
cp .env.example .env
php artisan key:generate

# 5. Run migrations
php artisan migrate --force

# 6. Cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 7. Set up cron job
* * * * * cd /var/www/domain-crm && php artisan schedule:run >> /dev/null 2>&1
```

---

## 11. Troubleshooting

### 11.1 Common Issues

#### Database Connection Error
```bash
# Check database file permissions
ls -la database/database.sqlite

# Recreate database file
touch database/database.sqlite
php artisan migrate
```

#### Token Authentication Issues
```bash
# Clear cache
php artisan cache:clear
php artisan config:clear

# Republish Sanctum
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider" --force
```

#### Permission Errors
```bash
# Fix storage permissions
sudo chown -R www-data:www-data storage
sudo chmod -R 775 storage
```

### 11.2 Debugging
```php
// Enable query logging
DB::enableQueryLog();
// Your code here
dd(DB::getQueryLog());

// Log custom messages
Log::info('Debug message', ['data' => $data]);
```

### 11.3 Performance Optimization
- Enable OPcache
- Use Redis for caching
- Optimize database queries
- Use eager loading
- Implement API rate limiting

---

## 12. Contributing

### 12.1 Development Workflow
1. Fork the repository
2. Create feature branch
3. Make changes
4. Write tests
5. Run test suite
6. Submit pull request

### 12.2 Code Standards
- Follow PSR-12 coding standards
- Use Laravel Pint for code formatting
- Write comprehensive tests
- Document new features
- Follow semantic versioning

### 12.3 Git Workflow
```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes and commit
git add .
git commit -m "Add new feature"

# Push and create PR
git push origin feature/new-feature
```

---

## Appendices

### A. API Response Formats

#### Success Response Format
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Operation completed successfully"
}
```

#### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "The given data was invalid.",
    "details": {
      "email": ["The email field is required."]
    }
  }
}
```

#### Pagination Response Format
```json
{
  "success": true,
  "data": {
    "current_page": 1,
    "data": [],
    "first_page_url": "http://localhost:8000/api/domains?page=1",
    "from": 1,
    "last_page": 10,
    "last_page_url": "http://localhost:8000/api/domains?page=10",
    "next_page_url": "http://localhost:8000/api/domains?page=2",
    "path": "http://localhost:8000/api/domains",
    "per_page": 15,
    "prev_page_url": null,
    "to": 15,
    "total": 150
  }
}
```

### B. Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| VALIDATION_ERROR | 422 | Request validation failed |
| UNAUTHORIZED | 401 | Authentication required |
| FORBIDDEN | 403 | Insufficient permissions |
| NOT_FOUND | 404 | Resource not found |
| SERVER_ERROR | 500 | Internal server error |
| RATE_LIMIT_EXCEEDED | 429 | Too many requests |
| INVALID_TOKEN | 401 | Invalid or expired token |
| DUPLICATE_ENTRY | 409 | Resource already exists |

### C. Database Schema

#### Complete Migration Files
```php
// 2024_01_01_000001_create_admins_table.php
Schema::create('admins', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->string('email')->unique();
    $table->timestamp('email_verified_at')->nullable();
    $table->string('password');
    $table->enum('role', ['admin', 'super_admin'])->default('admin');
    $table->boolean('is_active')->default(true);
    $table->rememberToken();
    $table->timestamps();
});

// 2024_01_01_000002_create_categories_table.php
Schema::create('categories', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->text('description')->nullable();
    $table->timestamps();
});

// 2024_01_01_000003_create_domains_table.php
Schema::create('domains', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->foreignId('category_id')->nullable()->constrained()->onDelete('set null');
    $table->decimal('price', 10, 2)->nullable();
    $table->enum('status', ['active', 'expired', 'pending'])->default('active');
    $table->date('expiry_date')->nullable();
    $table->text('description')->nullable();
    $table->timestamps();

    $table->index(['status', 'expiry_date']);
    $table->index('category_id');
});

// 2024_01_01_000004_create_simple_domains_table.php
Schema::create('simple_domains', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->decimal('price', 10, 2);
    $table->boolean('is_available')->default(true);
    $table->text('description')->nullable();
    $table->timestamps();
});
```

### D. Configuration Reference

#### Complete Environment Variables
```env
# Application
APP_NAME="Domain CRM"
APP_ENV=local
APP_KEY=base64:...
APP_DEBUG=true
APP_URL=http://localhost:8000
APP_TIMEZONE=UTC

# Database
DB_CONNECTION=sqlite
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=/absolute/path/to/database.sqlite
DB_USERNAME=
DB_PASSWORD=

# Cache
CACHE_DRIVER=file
CACHE_PREFIX=domain_crm

# Session
SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

# Queue
QUEUE_CONNECTION=sync

# Mail
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Sanctum
SANCTUM_STATEFUL_DOMAINS=localhost,127.0.0.1

# Logging
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Broadcasting
BROADCAST_DRIVER=log

# Filesystem
FILESYSTEM_DISK=local

# AWS (if using S3)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

# Pusher (if using real-time features)
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

# Vite
VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
```

### E. Changelog

#### Version 1.0.0 (2024-01-31)
**Added**
- Initial release
- Admin authentication system
- Domain management CRUD operations
- Category management
- Dashboard with statistics
- Email notifications for domain expiry
- Simple domain marketplace
- RESTful API with Laravel Sanctum
- Comprehensive admin panel
- Mobile-responsive design

**Security**
- Token-based authentication
- CSRF protection
- Input validation
- SQL injection prevention
- XSS protection

**Performance**
- Database indexing
- Query optimization
- Caching implementation
- API rate limiting

#### Version 1.1.0 (Planned)
**Planned Features**
- Advanced search and filtering
- Bulk operations
- Export functionality
- Advanced reporting
- Multi-language support
- API versioning
- Webhook support
- Advanced user roles

---

**Document Version**: 1.0
**Last Updated**: 2024-01-31
**Author**: Development Team
**Contact**: <EMAIL>
