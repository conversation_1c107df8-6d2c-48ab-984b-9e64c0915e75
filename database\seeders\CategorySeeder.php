<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    public function run(): void
    {
        $categories = [
            ['name' => 'Technology', 'color' => '#3b82f6'],
            ['name' => 'Business', 'color' => '#10b981'],
            ['name' => 'E-commerce', 'color' => '#f59e0b'],
            ['name' => 'Blog', 'color' => '#8b5cf6'],
            ['name' => 'Portfolio', 'color' => '#ef4444'],
            ['name' => 'News', 'color' => '#06b6d4'],
            ['name' => 'Education', 'color' => '#84cc16'],
            ['name' => 'Entertainment', 'color' => '#f97316'],
        ];

        foreach ($categories as $category) {
            Category::firstOrCreate(
                ['name' => $category['name']],
                ['color' => $category['color']]
            );
        }
    }
}