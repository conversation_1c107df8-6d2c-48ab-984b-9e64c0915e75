<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class DomainNameRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Check for spaces
        if (strpos($value, ' ') !== false) {
            $fail('The :attribute cannot contain spaces.');
            return;
        }

        // Check for uppercase letters
        if ($value !== strtolower($value)) {
            $fail('The :attribute must be in lowercase.');
            return;
        }

        // Check for valid domain name format (basic validation)
        if (!preg_match('/^[a-z0-9]([a-z0-9\-]{0,61}[a-z0-9])?$/', $value)) {
            $fail('The :attribute must be a valid domain name format (lowercase letters, numbers, and hyphens only).');
            return;
        }

        // Check if it starts or ends with hyphen
        if (str_starts_with($value, '-') || str_ends_with($value, '-')) {
            $fail('The :attribute cannot start or end with a hyphen.');
            return;
        }
    }
}
