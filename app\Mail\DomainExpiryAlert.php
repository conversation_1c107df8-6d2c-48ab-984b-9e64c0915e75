<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class DomainExpiryAlert extends Mailable
{
    use Queueable, SerializesModels;

    public $domains;
    public $days;

    /**
     * Create a new message instance.
     */
    public function __construct(Collection $domains, int $days)
    {
        $this->domains = $domains;
        $this->days = $days;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = $this->days === 1 
            ? 'URGENT: Domains Expiring Tomorrow!' 
            : "Domain Expiry Alert - {$this->days} Days Remaining";

        return new Envelope(
            subject: $subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.domain-expiry-alert',
            with: [
                'domains' => $this->domains,
                'days' => $this->days
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
