<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SimpleDomain;
use App\Models\Domain;
use App\Rules\DomainNameRule;
use Illuminate\Http\Request;

class SimpleDomainApiController extends Controller
{
    public function index(Request $request)
    {
        $query = SimpleDomain::query();

        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        if ($request->filled('category_id')) {
            $query->byCategory($request->category_id);
        }

        $domains = $query->orderBy('created_at', 'desc')->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $domains->items(),
            'pagination' => [
                'current_page' => $domains->currentPage(),
                'last_page' => $domains->lastPage(),
                'total' => $domains->total(),
                'per_page' => $domains->perPage(),
            ]
        ]);
    }

    public function store(Request $request)
    {
        $request->merge([
            'name' => $this->cleanDomainName($request->name)
        ]);

        $request->validate([
            'name' => ['required', 'string', new DomainNameRule, 'unique:simple_domains,name'],
            'categories' => 'required|array|min:1',
        ]);

        $simpleDomain = SimpleDomain::create($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Reserve domain created successfully',
            'data' => $simpleDomain
        ], 201);
    }

    public function show(SimpleDomain $simpleDomain)
    {
        return response()->json([
            'success' => true,
            'data' => $simpleDomain
        ]);
    }

    public function update(Request $request, SimpleDomain $simpleDomain)
    {
        $request->merge([
            'name' => $this->cleanDomainName($request->name)
        ]);

        $request->validate([
            'name' => ['required', 'string', new DomainNameRule, 'unique:simple_domains,name,' . $simpleDomain->id],
            'categories' => 'required|array|min:1',
        ]);

        $simpleDomain->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Reserve domain updated successfully',
            'data' => $simpleDomain
        ]);
    }

    public function destroy(SimpleDomain $simpleDomain)
    {
        $simpleDomain->delete();

        return response()->json([
            'success' => true,
            'message' => 'Reserve domain deleted successfully'
        ]);
    }

    public function buy(Request $request, SimpleDomain $simpleDomain)
    {
        $request->validate([
            'extensions' => 'required|array|min:1',
            'expiry_date' => 'required|date',
            'rating' => 'required|integer|min:1|max:5'
        ]);

        // Create domain from simple domain
        $domain = Domain::create([
            'name' => $simpleDomain->name,
            'extensions' => $request->extensions,
            'categories' => $simpleDomain->categories,
            'expiry_date' => $request->expiry_date,
            'rating' => $request->rating,
        ]);

        // Delete simple domain
        $simpleDomain->delete();

        return response()->json([
            'success' => true,
            'message' => 'Domain purchased successfully',
            'data' => $domain
        ]);
    }

    public function checkDuplicate(Request $request)
    {
        $query = SimpleDomain::where('name', $request->name);
        
        if ($request->has('ignore_id')) {
            $query->where('id', '!=', $request->ignore_id);
        }
        
        $exists = $query->exists();
        
        return response()->json([
            'success' => true,
            'exists' => $exists
        ]);
    }

    private function cleanDomainName($domainName)
    {
        if (!$domainName) {
            return $domainName;
        }
        
        return strtolower(str_replace(' ', '', trim($domainName)));
    }
}