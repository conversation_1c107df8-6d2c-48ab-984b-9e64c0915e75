# Domain CRM Management System

A comprehensive Flutter application for managing domain portfolios with advanced network connectivity handling and robust error management.

## Features

### 🌐 Advanced Network Handling
- **Multi-layer connectivity detection** with fallback mechanisms
- **Real-time network status monitoring** with visual indicators
- **Automatic retry logic** for failed network requests
- **Intelligent caching** to reduce unnecessary network calls
- **Multiple endpoint testing** (API server, Google DNS, Cloudflare DNS)

### 🔐 Enhanced Authentication
- **Secure login system** with comprehensive validation
- **Network-aware authentication** that checks connectivity before login attempts
- **Detailed error messages** for different failure scenarios
- **Session management** with automatic token validation

### 🎨 Modern UI/UX
- **Material Design 3** with custom theming
- **Responsive design** that adapts to different screen sizes
- **Smooth animations** and transitions
- **Real-time network status indicators**
- **Loading states** and progress indicators

### 🛡️ Robust Error Handling
- **Comprehensive error categorization** (network, timeout, auth, server)
- **User-friendly error messages** instead of technical jargon
- **Automatic error logging** in debug mode
- **Retry mechanisms** for recoverable errors

## API Configuration

The app is configured to work with the live API at:
```
https://domain.agfgroupindia.com
```

### API Endpoints
- **Login**: `/api/auth/login`
- **User Info**: `/api/auth/user`
- **Domains**: `/api/domains`
- **Categories**: `/api/categories`
- **Dashboard Stats**: `/api/dashboard/stats`

## Network Connectivity Features

### 1. Multi-Level Connectivity Testing
```dart
// Basic connectivity check
final hasBasicConnectivity = await Connectivity().checkConnectivity();

// Real internet connectivity test
final hasInternet = await NetworkUtils().isConnectedToInternet();

// API-specific connectivity test
final canReachAPI = await SimpleApiService().testConnection();
```

### 2. Automatic Retry Logic
- **3 retry attempts** for network-related failures
- **Exponential backoff** (2s, 4s, 6s delays)
- **Smart retry conditions** based on error type
- **Network cache clearing** between retries

### 3. Real-Time Network Monitoring
- **Stream-based network status updates**
- **Visual indicators** in the UI
- **Automatic error clearing** when connection is restored
- **Persistent network status banner**

## Error Handling System

### Error Categories
1. **Network Errors**: Connection issues, DNS failures, etc.
2. **Timeout Errors**: Request timeouts, connection timeouts
3. **Authentication Errors**: Invalid credentials, session expiry
4. **Server Errors**: 5xx HTTP status codes
5. **Validation Errors**: Invalid input data

### User-Friendly Messages
Instead of technical errors like "SocketException", users see:
- "No internet connection. Please check your network and try again."
- "Connection timeout. Please check your internet connection and try again."
- "Invalid email or password. Please check your credentials."

## Installation & Setup

### Prerequisites
- Flutter SDK (3.8.1 or higher)
- Dart SDK (3.8.1 or higher)
- Android Studio / VS Code
- Android device or emulator

### Installation Steps

1. **Clone the repository**
```bash
git clone <repository-url>
cd domain_flutter_code
```

2. **Install dependencies**
```bash
flutter pub get
```

3. **Run the app**
```bash
flutter run
```

### Dependencies
```yaml
dependencies:
  flutter:
    sdk: flutter
  http: ^1.1.0                    # HTTP client
  provider: ^6.1.1                # State management
  shared_preferences: ^2.2.2      # Local storage
  connectivity_plus: ^6.0.5       # Network connectivity
  internet_connection_checker: ^2.0.0  # Internet connectivity
  flutter_animate: ^4.5.2         # Animations
  google_fonts: ^6.2.1           # Custom fonts
```

## Troubleshooting

### Common Issues

#### 1. "No internet connection" Error
**Symptoms**: App shows network error even when connected to WiFi/mobile data

**Solutions**:
- Check if device has actual internet access (try opening a website)
- Restart the app to refresh network status
- Check if firewall/proxy is blocking the app
- Try switching between WiFi and mobile data

#### 2. Login Fails with "Cannot reach server"
**Symptoms**: Network connection is fine but login fails

**Solutions**:
- Verify the API URL is accessible: `https://domain.agfgroupindia.com`
- Check if the server is running and responding
- Verify API endpoints are correct
- Check for any server-side issues

#### 3. App Stuck on Loading Screen
**Symptoms**: App shows loading spinner indefinitely

**Solutions**:
- Force close and restart the app
- Clear app data/cache
- Check network connectivity
- Verify API server is responding

#### 4. Frequent Connection Timeouts
**Symptoms**: Requests timeout frequently even with good connection

**Solutions**:
- Check network stability
- Try connecting to a different network
- Increase timeout values in `SimpleApiService` if needed
- Contact server administrator if issue persists

### Debug Mode Features

When running in debug mode, the app provides detailed logging:
- Network connectivity test results
- API request/response details
- Error categorization and logging
- Network status changes

To enable debug logging, run:
```bash
flutter run --debug
```

### Network Configuration

The app uses multiple fallback mechanisms for network testing:

1. **Primary**: API health check (`/api/health`)
2. **Fallback 1**: Google DNS (`https://dns.google`)
3. **Fallback 2**: Cloudflare DNS (`https://*******`)

This ensures reliable connectivity detection even if one service is unavailable.
