import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../utils/theme.dart';

// Legacy ShimmerLoading - now uses Shimmer package internally
class ShimmerLoading extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final Color? baseColor;
  final Color? highlightColor;

  const ShimmerLoading({
    super.key,
    required this.child,
    required this.isLoading,
    this.baseColor,
    this.highlightColor,
  });

  @override
  Widget build(BuildContext context) {
    if (!isLoading) {
      return child;
    }

    return Shimmer.fromColors(
      baseColor: baseColor ?? AppTheme.dividerLight,
      highlightColor: highlightColor ?? AppTheme.surfaceLight,
      child: child,
    );
  }
}

class ShimmerListTile extends StatelessWidget {
  const ShimmerListTile({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppTheme.dividerLight,
      highlightColor: AppTheme.surfaceLight,
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spaceMD),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: AppTheme.dividerLight,
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
                const SizedBox(width: AppTheme.spaceMD),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: 16,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: AppTheme.dividerLight,
                          borderRadius: AppTheme.smallRadius,
                        ),
                      ),
                      const SizedBox(height: AppTheme.spaceSM),
                      Container(
                        height: 14,
                        width: 200,
                        decoration: BoxDecoration(
                          color: AppTheme.dividerLight,
                          borderRadius: AppTheme.smallRadius,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spaceMD),
            Container(
              height: 12,
              width: 150,
              decoration: BoxDecoration(
                color: AppTheme.dividerLight,
                borderRadius: AppTheme.smallRadius,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
