class ExpirySettings {
  final String adminEmail;
  final String alertDays;
  final bool isActive;
  final String? smtpHost;
  final int? smtpPort;
  final String? smtpUsername;
  final String? smtpPassword;
  final String? smtpEncryption;
  final String? mailFromAddress;
  final String? mailFromName;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  ExpirySettings({
    required this.adminEmail,
    required this.alertDays,
    required this.isActive,
    this.smtpHost,
    this.smtpPort,
    this.smtpUsername,
    this.smtpPassword,
    this.smtpEncryption,
    this.mailFromAddress,
    this.mailFromName,
    this.createdAt,
    this.updatedAt,
  });

  factory ExpirySettings.fromJson(Map<String, dynamic> json) {
    return ExpirySettings(
      adminEmail: json['admin_email'],
      alertDays: json['alert_days'],
      isActive: json['is_active'] ?? false,
      smtpHost: json['smtp_host'],
      smtpPort: json['smtp_port'],
      smtpUsername: json['smtp_username'],
      smtpPassword: json['smtp_password'],
      smtpEncryption: json['smtp_encryption'],
      mailFromAddress: json['mail_from_address'],
      mailFromName: json['mail_from_name'],
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'admin_email': adminEmail,
      'alert_days': alertDays,
      'is_active': isActive,
      'smtp_host': smtpHost,
      'smtp_port': smtpPort,
      'smtp_username': smtpUsername,
      'smtp_password': smtpPassword,
      'smtp_encryption': smtpEncryption,
      'mail_from_address': mailFromAddress,
      'mail_from_name': mailFromName,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  ExpirySettings copyWith({
    String? adminEmail,
    String? alertDays,
    bool? isActive,
    String? smtpHost,
    int? smtpPort,
    String? smtpUsername,
    String? smtpPassword,
    String? smtpEncryption,
    String? mailFromAddress,
    String? mailFromName,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ExpirySettings(
      adminEmail: adminEmail ?? this.adminEmail,
      alertDays: alertDays ?? this.alertDays,
      isActive: isActive ?? this.isActive,
      smtpHost: smtpHost ?? this.smtpHost,
      smtpPort: smtpPort ?? this.smtpPort,
      smtpUsername: smtpUsername ?? this.smtpUsername,
      smtpPassword: smtpPassword ?? this.smtpPassword,
      smtpEncryption: smtpEncryption ?? this.smtpEncryption,
      mailFromAddress: mailFromAddress ?? this.mailFromAddress,
      mailFromName: mailFromName ?? this.mailFromName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  List<int> get alertDaysArray {
    if (alertDays.isEmpty) return [30, 15, 7, 1];
    return alertDays
        .split(',')
        .map((day) => int.tryParse(day.trim()) ?? 0)
        .where((day) => day > 0)
        .toList();
  }

  String get alertDaysDisplay {
    final days = alertDaysArray;
    if (days.isEmpty) return 'No alerts configured';
    if (days.length == 1) return '${days.first} day${days.first == 1 ? '' : 's'}';
    return '${days.sublist(0, days.length - 1).join(', ')} and ${days.last} days';
  }

  bool get hasSmtpConfig {
    return smtpHost != null && 
           smtpUsername != null && 
           smtpPassword != null && 
           smtpPassword!.isNotEmpty;
  }
}
