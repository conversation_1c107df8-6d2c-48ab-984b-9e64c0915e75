<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\Domain;
use App\Models\SimpleDomain;

class TestDataSeeder extends Seeder
{
    public function run()
    {
        // Create categories
        $categories = [
            ['name' => 'Technology', 'color' => '#3498db'],
            ['name' => 'Business', 'color' => '#2ecc71'],
            ['name' => 'Entertainment', 'color' => '#e74c3c'],
            ['name' => 'Education', 'color' => '#f39c12'],
            ['name' => 'Health', 'color' => '#9b59b6'],
        ];

        foreach ($categories as $category) {
            Category::firstOrCreate(['name' => $category['name']], $category);
        }

        $categoryIds = Category::pluck('id')->toArray();

        // Create domains
        $domains = [
            [
                'name' => 'techstartup',
                'extensions' => ['.com', '.net'],
                'categories' => [$categoryIds[0], $categoryIds[1]],
                'expiry_date' => now()->addDays(365),
                'rating' => 5,
                'is_available' => true,
            ],
            [
                'name' => 'mybusiness',
                'extensions' => ['.com', '.org'],
                'categories' => [$categoryIds[1]],
                'expiry_date' => now()->addDays(30),
                'rating' => 4,
                'is_available' => false,
            ],
            [
                'name' => 'gameworld',
                'extensions' => ['.com', '.io'],
                'categories' => [$categoryIds[2]],
                'expiry_date' => now()->addDays(180),
                'rating' => 3,
                'is_available' => true,
            ],
        ];

        foreach ($domains as $domain) {
            Domain::firstOrCreate(['name' => $domain['name']], $domain);
        }

        // Create simple domains
        $simpleDomains = [
            [
                'name' => 'reserveddomain1',
                'categories' => [$categoryIds[0]],
            ],
            [
                'name' => 'reserveddomain2',
                'categories' => [$categoryIds[1], $categoryIds[2]],
            ],
            [
                'name' => 'reserveddomain3',
                'categories' => [$categoryIds[3]],
            ],
        ];

        foreach ($simpleDomains as $simpleDomain) {
            SimpleDomain::firstOrCreate(['name' => $simpleDomain['name']], $simpleDomain);
        }

        $this->command->info('Test data created successfully!');
    }
}