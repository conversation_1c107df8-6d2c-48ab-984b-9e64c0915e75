<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DomainController;
use App\Http\Controllers\SimpleDomainController;
use App\Http\Controllers\CategoryController;

// Redirect root to admin login
Route::get('/', function () {
    return redirect()->route('admin.login');
});

// All main routes require admin authentication
Route::middleware('auth:admin')->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/analytics', [DashboardController::class, 'getAnalytics'])->name('dashboard.analytics');

    // Domain routes
    Route::resource('domains', DomainController::class);
    Route::post('/domains/check-availability', [DomainController::class, 'checkAvailability'])->name('domains.check-availability');
    Route::post('/domains/check-duplicate', [DomainController::class, 'checkDuplicate'])->name('domains.checkDuplicate');

    // Simple Domain routes
    Route::resource('simple-domains', SimpleDomainController::class);
    Route::get('/simple-domains/{simpleDomain}/buy', [SimpleDomainController::class, 'buy'])->name('simple-domains.buy');
    Route::post('/simple-domains/check-duplicate', [SimpleDomainController::class, 'checkDuplicate'])->name('simple-domains.checkDuplicate');

    // Category routes
    Route::resource('categories', CategoryController::class);
});

// Admin Authentication Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Guest routes (login)
    Route::middleware('guest:admin')->group(function () {
        Route::get('/login', [App\Http\Controllers\Auth\AdminLoginController::class, 'showLoginForm'])->name('login');
        Route::post('/login', [App\Http\Controllers\Auth\AdminLoginController::class, 'login']);
    });
    
    // Authenticated admin routes
    Route::middleware('auth:admin')->group(function () {
        Route::post('/logout', [App\Http\Controllers\Auth\AdminLoginController::class, 'logout'])->name('logout');
        Route::get('/dashboard', [App\Http\Controllers\AdminController::class, 'dashboard'])->name('dashboard');
        Route::get('/settings', [App\Http\Controllers\AdminController::class, 'settings'])->name('settings');
        Route::put('/settings', [App\Http\Controllers\AdminController::class, 'updateSettings'])->name('settings.update');
        Route::get('/system-settings', [App\Http\Controllers\AdminController::class, 'systemSettings'])->name('system-settings');
        Route::put('/system-settings', [App\Http\Controllers\AdminController::class, 'updateSystemSettings'])->name('system-settings.update');
        
        // Expiry Settings Routes
        Route::get('/expiry-settings', [App\Http\Controllers\Admin\ExpirySettingsController::class, 'index'])->name('expiry-settings');
        Route::put('/expiry-settings', [App\Http\Controllers\Admin\ExpirySettingsController::class, 'update'])->name('expiry-settings.update');
        Route::get('/expiry-settings/test-email', [App\Http\Controllers\Admin\ExpirySettingsController::class, 'testEmail'])->name('expiry-settings.test-email');
    });
});
