@extends('layouts.app')

@section('title', 'Identify Domains - Domain CRM')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-bookmark me-2 text-primary"></i>
        Identify Domains
    </h1>
    <a href="{{ route('simple-domains.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        Add Identify Domain
    </a>
</div>

<!-- A-Z Alphabetical Filter -->
<div class="card mb-3">
    <div class="card-body py-3">
        <div class="d-flex flex-wrap align-items-center">
            <span class="me-3 fw-bold text-muted">Filter by Letter:</span>
            <div class="d-flex flex-wrap gap-1">
                @foreach(range('A', 'Z') as $letter)
                    <a href="{{ route('simple-domains.index', array_merge(request()->all(), ['letter' => $letter])) }}" 
                       class="btn btn-sm {{ request('letter') == $letter ? 'btn-primary' : 'btn-outline-primary' }} letter-filter">
                        {{ $letter }}
                    </a>
                @endforeach
                @if(request('letter'))
                    <a href="{{ route('simple-domains.index', request()->except('letter')) }}" 
                       class="btn btn-sm btn-outline-secondary ms-2">
                        <i class="fas fa-times"></i> Clear
                    </a>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('simple-domains.index') }}" class="row g-3">
            <input type="hidden" name="letter" value="{{ request('letter') }}">
            <div class="col-md-4">
                <label for="domain_name" class="form-label">Domain Name</label>
                <input type="text" class="form-control" id="domain_name" name="domain_name" 
                       value="{{ request('domain_name') }}" placeholder="Search by domain name">
            </div>
            <div class="col-md-4">
                <label for="category_id" class="form-label">Category</label>
                <select class="form-select" id="category_id" name="category_id">
                    <option value="">All Categories</option>
                    @foreach($categories as $category)
                        <option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
                            {{ $category->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search me-1"></i>
                    Filter
                </button>
                <a href="{{ route('simple-domains.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>
                    Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Simple Domains List -->
<div class="card">
    <div class="card-body">
        @if($simpleDomains->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Domain Name</th>
                            <th>Categories</th>
                            <th>Created Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($simpleDomains as $domain)
                            <tr>
                                <td>
                                    <strong>{{ ucfirst($domain->name) }}</strong>
                                </td>
                                <td>
                                    @foreach($domain->category_details as $category)
                                        <span class="badge me-1" style="background-color: {{ $category->color }}">
                                            {{ $category->name }}
                                        </span>
                                    @endforeach
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ $domain->created_at->format('M d, Y') }}
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('simple-domains.buy', $domain) }}" 
                                           class="btn btn-sm btn-success" title="Buy Domain">
                                            <i class="fas fa-shopping-cart"></i>
                                            Buy
                                        </a>
                                        <a href="{{ route('simple-domains.show', $domain) }}" 
                                           class="btn btn-sm btn-outline-info" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('simple-domains.edit', $domain) }}" 
                                           class="btn btn-sm btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteDomain({{ $domain->id }})" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-between align-items-center mt-4 pt-3 border-top">
                <div class="pagination-info">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Showing {{ $simpleDomains->firstItem() }} to {{ $simpleDomains->lastItem() }} 
                        of {{ $simpleDomains->total() }} results
                    </small>
                </div>
                <div class="pagination-wrapper">
                    {{ $simpleDomains->appends(request()->query())->links('pagination::bootstrap-4') }}
                </div>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-bookmark fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Identify Domains Found</h5>
                <p class="text-muted">Start by adding your first identify domain.</p>
                <a href="{{ route('simple-domains.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    Add Identify Domain
                </a>
            </div>
        @endif
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this reserve domain? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.pagination-wrapper .pagination {
    margin-bottom: 0;
}

.pagination-wrapper .page-link {
    color: #6c757d;
    border: 1px solid #dee2e6;
    padding: 0.5rem 0.75rem;
    margin: 0 2px;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
}

.pagination-wrapper .page-link:hover {
    color: #0d6efd;
    background-color: #e9ecef;
    border-color: #dee2e6;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pagination-wrapper .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
    box-shadow: 0 2px 4px rgba(13,110,253,0.25);
}

.pagination-wrapper .page-item.disabled .page-link {
    color: #adb5bd;
    background-color: #fff;
    border-color: #dee2e6;
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

@media (max-width: 768px) {
    .d-flex.justify-content-between.align-items-center.mt-4.pt-3.border-top {
        flex-direction: column;
        gap: 1rem;
        align-items: center !important;
    }
    
    .pagination-info {
        order: 2;
    }
    
    .pagination-wrapper {
        order: 1;
    }
}
</style>
@endpush

@push('scripts')
<script>
let domainToDelete = null;

function deleteDomain(domainId) {
    domainToDelete = domainId;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

document.getElementById('confirmDelete').addEventListener('click', function() {
    if (domainToDelete) {
        // Create and submit delete form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/simple-domains/${domainToDelete}`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        
        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
});
</script>
@endpush