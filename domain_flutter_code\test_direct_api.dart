import 'dart:convert';
import 'package:http/http.dart' as http;

void main() async {
  print('=== TESTING DIRECT API APPROACH ===');
  
  const baseUrl = 'https://domain.agfgroupindia.com';
  const loginEndpoint = '/api/auth/login';
  
  try {
    print('\n1. Testing server connectivity...');
    final serverResponse = await http.get(Uri.parse(baseUrl))
        .timeout(const Duration(seconds: 10));
    print('✅ Server reachable: ${serverResponse.statusCode}');
    
    print('\n2. Testing login endpoint with test credentials...');
    final loginResponse = await http.post(
      Uri.parse('$baseUrl$loginEndpoint'),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: json.encode({
        'email': '<EMAIL>',
        'password': 'testpassword',
      }),
    ).timeout(const Duration(seconds: 30));
    
    print('Status: ${loginResponse.statusCode}');
    print('Headers: ${loginResponse.headers}');
    print('Body: ${loginResponse.body}');
    
    if (loginResponse.statusCode == 422) {
      print('✅ Login endpoint working correctly (invalid credentials response)');
    } else if (loginResponse.statusCode == 200) {
      print('✅ Login endpoint working (successful login)');
    } else {
      print('⚠️ Unexpected response code: ${loginResponse.statusCode}');
    }
    
    print('\n3. Testing with direct HTTP client approach...');
    final client = http.Client();
    try {
      final directResponse = await client.post(
        Uri.parse('$baseUrl$loginEndpoint'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'Domain-CRM-Flutter-App/1.0',
        },
        body: json.encode({
          'email': '<EMAIL>',
          'password': 'testpassword',
        }),
      ).timeout(const Duration(seconds: 30));
      
      print('Direct client status: ${directResponse.statusCode}');
      print('Direct client body: ${directResponse.body}');
      
      if (directResponse.statusCode >= 200 && directResponse.statusCode < 500) {
        print('✅ Direct HTTP client approach working');
      }
    } finally {
      client.close();
    }
    
    print('\n🎉 All tests completed successfully!');
    print('The API is working correctly. The issue might be:');
    print('1. Missing Android permissions in the APK');
    print('2. Network security configuration');
    print('3. Different behavior in release vs debug mode');
    
  } catch (e) {
    print('❌ Test failed: $e');
    print('Error type: ${e.runtimeType}');
    
    if (e.toString().contains('SocketException')) {
      print('💡 Network connectivity issue detected');
    } else if (e.toString().contains('TimeoutException')) {
      print('💡 Request timeout - server might be slow');
    } else {
      print('💡 Unexpected error occurred');
    }
  }
}
