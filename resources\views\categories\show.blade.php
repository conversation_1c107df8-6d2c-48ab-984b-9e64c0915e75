@extends('layouts.app')

@section('title', $category->name . ' - Domain CRM')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <span class="badge me-2" style="background-color: {{ $category->color }}; color: white;">
            {{ $category->name }}
        </span>
        Category Details
    </h1>
    <div>
        <a href="{{ route('categories.edit', $category) }}" class="btn btn-warning me-2">
            <i class="fas fa-edit me-1"></i>
            Edit Category
        </a>
        <a href="{{ route('categories.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>
            Back to Categories
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Category Information</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label text-muted">Name</label>
                    <div class="fw-bold">{{ $category->name }}</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted">Color</label>
                    <div>
                        <span class="badge" style="background-color: {{ $category->color }}; color: white;">
                            {{ $category->color }}
                        </span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted">Total Domains</label>
                    <div class="fw-bold">{{ $domains->total() }}</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted">Created</label>
                    <div>{{ $category->created_at->format('M d, Y \a\t g:i A') }}</div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted">Last Updated</label>
                    <div>{{ $category->updated_at->format('M d, Y \a\t g:i A') }}</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Domains in this Category</h5>
            </div>
            <div class="card-body">
                @if($domains->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Domain Name</th>
                                    <th>Extensions</th>
                                    <th>Expiry Date</th>
                                    <th>Rating</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($domains as $domain)
                                    <tr>
                                        <td>
                                            <strong>{{ $domain->name }}</strong>
                                        </td>
                                        <td>
                                            @foreach($domain->extensions as $ext)
                                                <span class="badge bg-info me-1">{{ $ext }}</span>
                                            @endforeach
                                        </td>
                                        <td>{{ $domain->expiry_date->format('M d, Y') }}</td>
                                        <td>
                                            <div class="star-rating">
                                                @for($i = 1; $i <= 5; $i++)
                                                    <i class="fas fa-star{{ $i <= $domain->rating ? '' : '-o' }}"></i>
                                                @endfor
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('domains.show', $domain) }}" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('domains.edit', $domain) }}" 
                                                   class="btn btn-sm btn-outline-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $domains->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <i class="fas fa-globe fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No domains in this category</h5>
                        <p class="text-muted">This category doesn't have any domains assigned yet.</p>
                        <a href="{{ route('domains.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            Add Domain
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection