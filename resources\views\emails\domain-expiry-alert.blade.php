<!DOCTYPE html>
<html>
<head>
    <title>Domain Expiry Alert</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 3px solid #007bff;
            margin-bottom: 20px;
        }
        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .critical {
            color: #721c24;
            background-color: #f8d7da;
        }
        .warning {
            color: #856404;
            background-color: #fff3cd;
        }
        .footer {
            margin-top: 30px;
            padding-top: 10px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Domain Expiry Alert</h1>
    </div>

    <div class="alert-info">
        <p>This is an automated alert for domains expiring in <strong>{{ $days }} days</strong>.</p>
    </div>

    <h2>Domains Requiring Attention</h2>
    
    <table>
        <thead>
            <tr>
                <th>Domain Name</th>
                <th>Expiry Date</th>
                <th>Days Left</th>
                <th>Categories</th>
                <th>Rating</th>
            </tr>
        </thead>
        <tbody>
            @foreach($domains as $domain)
            <tr class="{{ $domain->expiry_status }}">
                <td>{{ $domain->name }}</td>
                <td>{{ $domain->expiry_date->format('Y-m-d') }}</td>
                <td>{{ $domain->days_left }}</td>
                <td>{{ implode(', ', $domain->category_names) }}</td>
                <td>{{ $domain->rating }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <p>Please take appropriate action to renew these domains if needed.</p>

    <div class="footer">
        <p>This is an automated message from your Domain CRM Management System.</p>
    </div>
</body>
</html>