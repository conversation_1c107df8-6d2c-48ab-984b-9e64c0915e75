@extends('layouts.app')

@section('title', 'System Settings - Domain CRM')

@section('content')
<div class="row">
    <div class="col-md-8">
        <!-- Application Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    Application Settings
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.system-settings.update') }}">
                    @csrf
                    @method('PUT')
                    
                    <!-- App Name -->
                    <div class="mb-3">
                        <label for="app_name" class="form-label">Application Name *</label>
                        <input type="text" 
                               class="form-control @error('app_name') is-invalid @enderror" 
                               id="app_name" 
                               name="app_name" 
                               value="{{ old('app_name', $settings['app_name']) }}" 
                               required>
                        @error('app_name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">This will appear in the browser title and throughout the application.</small>
                    </div>
                    
                    <!-- App URL -->
                    <div class="mb-3">
                        <label for="app_url" class="form-label">Application URL *</label>
                        <input type="url" 
                               class="form-control @error('app_url') is-invalid @enderror" 
                               id="app_url" 
                               name="app_url" 
                               value="{{ old('app_url', $settings['app_url']) }}" 
                               required>
                        @error('app_url')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">The base URL for your application.</small>
                    </div>
                    
                    <!-- Timezone -->
                    <div class="mb-3">
                        <label for="timezone" class="form-label">Timezone *</label>
                        <select class="form-select @error('timezone') is-invalid @enderror" 
                                id="timezone" 
                                name="timezone" 
                                required>
                            <option value="">Select Timezone</option>
                            @php
                                $timezones = [
                                    'UTC' => 'UTC',
                                    'America/New_York' => 'Eastern Time (US & Canada)',
                                    'America/Chicago' => 'Central Time (US & Canada)',
                                    'America/Denver' => 'Mountain Time (US & Canada)',
                                    'America/Los_Angeles' => 'Pacific Time (US & Canada)',
                                    'Europe/London' => 'London',
                                    'Europe/Paris' => 'Paris',
                                    'Asia/Tokyo' => 'Tokyo',
                                    'Asia/Shanghai' => 'Shanghai',
                                    'Asia/Kolkata' => 'India Standard Time',
                                    'Australia/Sydney' => 'Sydney',
                                ];
                            @endphp
                            @foreach($timezones as $value => $label)
                                <option value="{{ $value }}" {{ old('timezone', $settings['timezone']) === $value ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                        @error('timezone')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Save Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Email Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-envelope me-2"></i>
                    Email Configuration
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Email settings are configured in the .env file. Contact your system administrator to modify these settings.
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <strong>Mail Driver:</strong>
                        <p class="text-muted">{{ $settings['mail_driver'] ?? 'Not configured' }}</p>
                    </div>
                    <div class="col-md-6">
                        <strong>Status:</strong>
                        <span class="badge {{ $settings['mail_driver'] ? 'bg-success' : 'bg-warning' }}">
                            {{ $settings['mail_driver'] ? 'Configured' : 'Not Configured' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Maintenance -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    Maintenance
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <h6>Clear Cache</h6>
                        <p class="text-muted">Clear application cache for better performance.</p>
                        <button class="btn btn-outline-warning" onclick="clearCache()">
                            <i class="fas fa-broom me-1"></i>
                            Clear Cache
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- System Information Sidebar -->
    <div class="col-md-4">
        <!-- System Status -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-server me-2"></i>
                    System Status
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Application:</span>
                        <span class="badge bg-success">Online</span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Database:</span>
                        <span class="badge bg-success">Connected</span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Storage:</span>
                        <span class="badge bg-success">Writable</span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Cache:</span>
                        <span class="badge bg-success">Active</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Version Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Version Information
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>Domain CRM:</strong>
                    <span class="float-end">v1.0.0</span>
                </div>
                <div class="mb-2">
                    <strong>Laravel:</strong>
                    <span class="float-end">{{ app()->version() }}</span>
                </div>
                <div class="mb-2">
                    <strong>PHP:</strong>
                    <span class="float-end">{{ PHP_VERSION }}</span>
                </div>
                <div class="mb-2">
                    <strong>Environment:</strong>
                    <span class="float-end">
                        <span class="badge {{ app()->environment() === 'production' ? 'bg-success' : 'bg-warning' }}">
                            {{ ucfirst(app()->environment()) }}
                        </span>
                    </span>
                </div>
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Quick Stats
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>Total Domains:</strong>
                    <span class="float-end">{{ \App\Models\Domain::count() }}</span>
                </div>
                <div class="mb-2">
                    <strong>Categories:</strong>
                    <span class="float-end">{{ \App\Models\Category::count() }}</span>
                </div>
                <div class="mb-2">
                    <strong>Admin Users:</strong>
                    <span class="float-end">{{ \App\Models\Admin::count() }}</span>
                </div>
                <div class="mb-2">
                    <strong>Disk Usage:</strong>
                    <span class="float-end">
                        <span class="badge bg-info">{{ number_format(disk_free_space('.') / 1024 / 1024 / 1024, 1) }}GB Free</span>
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function clearCache() {
    if (confirm('Are you sure you want to clear the application cache?')) {
        // Show loading state
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Clearing...';
        btn.disabled = true;
        
        // Simulate cache clearing (replace with actual cache clearing logic)
        setTimeout(() => {
            alert('Cache cleared successfully!');
            btn.innerHTML = originalText;
            btn.disabled = false;
        }, 2000);
    }
}
</script>
@endpush