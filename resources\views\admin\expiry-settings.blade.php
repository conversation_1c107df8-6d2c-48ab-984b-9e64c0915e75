@extends('layouts.admin')

@section('title', 'Expiry Settings')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Domain Expiry Alert Settings</h3>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    <form action="{{ route('admin.expiry-settings.update') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- Alert Settings -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0"><PERSON><PERSON> Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="admin_email">Admin Email for Alerts</label>
                                    <input type="email" class="form-control @error('admin_email') is-invalid @enderror" 
                                        id="admin_email" name="admin_email" 
                                        value="{{ old('admin_email', $settings->admin_email) }}" required>
                                    @error('admin_email')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                    <small class="form-text text-muted">
                                        Expiry alerts will be sent to this email address.
                                    </small>
                                </div>

                                <div class="form-group">
                                    <label for="alert_days">Alert Days Before Expiry</label>
                                    <input type="text" class="form-control @error('alert_days') is-invalid @enderror" 
                                        id="alert_days" name="alert_days" 
                                        value="{{ old('alert_days', $settings->alert_days) }}" required
                                        placeholder="10,20,30">
                                    @error('alert_days')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                    <small class="form-text text-muted">
                                        Enter comma-separated values for days before expiry when alerts should be sent (e.g., 10,20,30).
                                    </small>
                                </div>

                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" 
                                            {{ $settings->is_active ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_active">Enable Expiry Alerts</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- SMTP Settings -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Gmail SMTP Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <strong>Gmail Setup Instructions:</strong>
                                    <ol class="mb-0 mt-2">
                                        <li>Enable 2-Step Verification in your Google Account</li>
                                        <li>Go to Google Account > Security > 2-Step Verification > App passwords</li>
                                        <li>Generate an App Password for "Mail"</li>
                                        <li>Use the generated App Password (not your regular password) below</li>
                                    </ol>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="smtp_host">SMTP Host</label>
                                            <input type="text" class="form-control @error('smtp_host') is-invalid @enderror" 
                                                id="smtp_host" name="smtp_host" 
                                                value="{{ old('smtp_host', $settings->smtp_host ?: 'smtp.gmail.com') }}">
                                            @error('smtp_host')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="smtp_port">SMTP Port</label>
                                            <input type="number" class="form-control @error('smtp_port') is-invalid @enderror" 
                                                id="smtp_port" name="smtp_port" 
                                                value="{{ old('smtp_port', $settings->smtp_port ?: 587) }}" min="1" max="65535">
                                            @error('smtp_port')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="smtp_username">Gmail Address</label>
                                    <input type="email" class="form-control @error('smtp_username') is-invalid @enderror" 
                                        id="smtp_username" name="smtp_username" 
                                        value="{{ old('smtp_username', $settings->smtp_username) }}"
                                        placeholder="<EMAIL>">
                                    @error('smtp_username')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="smtp_password">Gmail App Password</label>
                                    <input type="password" class="form-control @error('smtp_password') is-invalid @enderror" 
                                        id="smtp_password" name="smtp_password" 
                                        value="{{ old('smtp_password', $settings->smtp_password) }}"
                                        placeholder="Enter your Gmail App Password">
                                    @error('smtp_password')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                    <small class="form-text text-muted">
                                        Use the App Password generated from your Google Account, not your regular password.
                                    </small>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="smtp_encryption">Encryption</label>
                                            <select class="form-control @error('smtp_encryption') is-invalid @enderror" 
                                                id="smtp_encryption" name="smtp_encryption">
                                                <option value="tls" {{ old('smtp_encryption', $settings->smtp_encryption) === 'tls' ? 'selected' : '' }}>TLS (Recommended)</option>
                                                <option value="ssl" {{ old('smtp_encryption', $settings->smtp_encryption) === 'ssl' ? 'selected' : '' }}>SSL</option>
                                            </select>
                                            @error('smtp_encryption')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="mail_from_name">From Name</label>
                                    <input type="text" class="form-control @error('mail_from_name') is-invalid @enderror" 
                                        id="mail_from_name" name="mail_from_name" 
                                        value="{{ old('mail_from_name', $settings->mail_from_name ?: config('app.name')) }}"
                                        placeholder="Domain CRM System">
                                    @error('mail_from_name')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                    <small class="form-text text-muted">
                                        This name will appear as the sender in email alerts.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Save Settings</button>
                            <a href="{{ route('admin.expiry-settings.test-email') }}" class="btn btn-info ml-2">
                                Send Test Email
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection