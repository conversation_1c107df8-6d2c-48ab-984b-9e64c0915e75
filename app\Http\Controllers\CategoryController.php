<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Domain;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    public function index(Request $request)
    {
        $categories = Category::orderBy('name')->get();

        if ($request->ajax()) {
            return response()->json(['categories' => $categories]);
        }

        return view('categories.index', compact('categories'));
    }

    public function create()
    {
        return view('categories.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|unique:categories,name',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/'
        ]);

        $category = Category::create($request->all());

        if ($request->ajax()) {
            return response()->json(['success' => true, 'category' => $category]);
        }

        return redirect()->route('categories.index')->with('success', 'Category created successfully!');
    }

    public function show(Category $category)
    {
        $domains = Domain::whereJsonContains('categories', $category->id)->paginate(10);
        return view('categories.show', compact('category', 'domains'));
    }

    public function edit(Category $category)
    {
        return view('categories.edit', compact('category'));
    }

    public function update(Request $request, Category $category)
    {
        $request->validate([
            'name' => 'required|string|unique:categories,name,' . $category->id,
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/'
        ]);

        $category->update($request->all());

        if ($request->ajax()) {
            return response()->json(['success' => true, 'category' => $category]);
        }

        return redirect()->route('categories.index')->with('success', 'Category updated successfully!');
    }

    public function destroy(Category $category)
    {
        // Check if category is being used by any domains
        $domainsCount = Domain::whereJsonContains('categories', $category->id)->count();
        
        if ($domainsCount > 0) {
            if (request()->ajax()) {
                return response()->json(['error' => 'Cannot delete category. It is being used by ' . $domainsCount . ' domain(s).'], 422);
            }
            return redirect()->route('categories.index')->with('error', 'Cannot delete category. It is being used by ' . $domainsCount . ' domain(s).');
        }

        $category->delete();

        if (request()->ajax()) {
            return response()->json(['success' => true]);
        }

        return redirect()->route('categories.index')->with('success', 'Category deleted successfully!');
    }
}
