@extends('layouts.app')

@section('title', 'Edit Domain - Domain CRM')

@push('styles')
<style>
.extension-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.9em;
}

.extension-badge.available {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.extension-checkbox:checked + .extension-label .extension-badge {
    font-weight: 600;
    box-shadow: 0 0 0 2px rgba(0,123,255,.25);
}

#addMoreExtensions {
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
@endpush

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-edit me-2 text-primary"></i>
        Edit Domain
    </h1>
    <div>
        <a href="{{ route('domains.show', $domain) }}" class="btn btn-outline-info me-2">
            <i class="fas fa-eye me-1"></i>
            View Domain
        </a>
        <a href="{{ route('domains.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>
            Back to Domains
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form id="domainForm" method="POST" action="{{ route('domains.update', $domain) }}">
                    @csrf
                    @method('PUT')
                    
                    <!-- Domain Name -->
                    <div class="mb-3">
                        <label for="name" class="form-label">Domain Name *</label>
                        <div class="input-group">
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $domain->name) }}" 
                                   placeholder="example" required>
                            <button type="button" class="btn btn-outline-primary" id="checkDuplicateBtn">
                                <i class="fas fa-search"></i>
                                Check
                            </button>
                        </div>
                        <div id="duplicateCheck" class="mt-2"></div>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Extensions -->
                    <div class="mb-3">
                        <label class="form-label">Extensions *</label>
                        <div id="extensionsContainer">
                            <!-- Default Extensions (.in and .com) -->
                            <div class="row" id="defaultExtensions">
                                @php
                                    $defaultExtensions = ['.in', '.com'];
                                    $selectedExtensions = old('extensions', $domain->extensions ?? []);
                                    $customExtensions = array_diff($selectedExtensions, $defaultExtensions);
                                @endphp
                                @foreach($defaultExtensions as $ext)
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input extension-checkbox" type="checkbox" 
                                                   name="extensions[]" value="{{ $ext }}" 
                                                   id="ext_{{ str_replace('.', '', $ext) }}"
                                                   {{ in_array($ext, $selectedExtensions) ? 'checked' : '' }}>
                                            <label class="form-check-label extension-label" for="ext_{{ str_replace('.', '', $ext) }}">
                                                <span class="extension-badge available">{{ $ext }}</span>
                                            </label>
                                        </div>
                                    </div>
                                @endforeach
                                
                                <!-- Custom Extensions (from existing domain) -->
                                @foreach($customExtensions as $ext)
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check position-relative">
                                            <input class="form-check-input extension-checkbox" type="checkbox" 
                                                   name="extensions[]" value="{{ $ext }}" 
                                                   id="ext_{{ str_replace('.', '', $ext) }}" checked>
                                            <label class="form-check-label extension-label" for="ext_{{ str_replace('.', '', $ext) }}">
                                                <span class="extension-badge available">{{ $ext }}</span>
                                            </label>
                                            <button type="button" class="btn btn-sm btn-outline-danger delete-extension" 
                                                    style="position: absolute; top: -5px; right: -5px; width: 20px; height: 20px; padding: 0; font-size: 10px; border-radius: 50%;"
                                                    title="Delete extension">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                @endforeach
                                
                                <!-- Add Extensions Button -->
                                <div class="col-md-4 mb-2">
                                    <button type="button" class="btn btn-outline-primary btn-sm" id="addMoreExtensions">
                                        <i class="fas fa-plus me-1"></i>
                                        Add Extensions
                                    </button>
                                </div>
                            </div>
                        </div>
                        @error('extensions')
                            <div class="text-danger small">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Categories -->
                    <div class="mb-3">
                        <label class="form-label">Categories *</label>
                        <div class="row">
                            @php
                                $selectedCategories = old('categories', $domain->categories ?? []);
                            @endphp
                            @foreach($categories as $category)
                                <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="categories[]" value="{{ $category->id }}" 
                                               id="cat{{ $category->id }}"
                                               {{ in_array($category->id, $selectedCategories) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="cat{{ $category->id }}">
                                            <span class="badge" style="background-color: {{ $category->color }}">
                                                {{ $category->name }}
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        @error('categories')
                            <div class="text-danger small">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Expiry Date -->
                    <div class="mb-3">
                        <label for="expiry_date" class="form-label">Expiry Date *</label>
                        <input type="date" class="form-control @error('expiry_date') is-invalid @enderror" 
                               id="expiry_date" name="expiry_date" 
                               value="{{ old('expiry_date', $domain->expiry_date->format('Y-m-d')) }}" 
                               required>
                        <div class="form-text">Enter the domain expiry date (past dates allowed)</div>
                        @error('expiry_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Rating -->
                    <div class="mb-3">
                        <label for="rating" class="form-label">Rating *</label>
                        <select class="form-select @error('rating') is-invalid @enderror" 
                                id="rating" name="rating" required>
                            <option value="">Select Rating</option>
                            @for($i = 1; $i <= 5; $i++)
                                <option value="{{ $i }}" {{ old('rating', $domain->rating) == $i ? 'selected' : '' }}>
                                    {{ $i }} Star{{ $i > 1 ? 's' : '' }}
                                </option>
                            @endfor
                        </select>
                        @error('rating')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ route('domains.show', $domain) }}" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Update Domain
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Extension Modal -->
<div class="modal fade" id="extensionModal" tabindex="-1" aria-labelledby="extensionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="extensionModalLabel">
                    <i class="fas fa-plus me-2"></i>Add Custom Extension
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="customExtension" class="form-label">Extension Name</label>
                    <div class="input-group">
                        <span class="input-group-text">.</span>
                        <input type="text" class="form-control" id="customExtension" placeholder="net, org, co" autocomplete="off">
                    </div>
                    <div class="form-text">Enter extension without the dot (e.g., net, org, co)</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="addExtensionBtn">
                    <i class="fas fa-plus me-1"></i>Add Extension
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const addMoreBtn = document.getElementById('addMoreExtensions');
    const checkDuplicateBtn = document.getElementById('checkDuplicateBtn');
    const nameInput = document.getElementById('name');
    const duplicateCheck = document.getElementById('duplicateCheck');
    const originalName = '{{ $domain->name }}';

    // Modal elements
    const extensionModal = new bootstrap.Modal(document.getElementById('extensionModal'));
    const customExtensionInput = document.getElementById('customExtension');
    const addExtensionBtn = document.getElementById('addExtensionBtn');

    // Add Extensions functionality - Show modal
    addMoreBtn.addEventListener('click', function() {
        customExtensionInput.value = '';
        extensionModal.show();
        setTimeout(() => customExtensionInput.focus(), 300);
    });

    // Handle modal add extension button
    addExtensionBtn.addEventListener('click', function() {
        const customExtension = customExtensionInput.value.trim();
        if (customExtension) {
            let ext = '.' + customExtension.toLowerCase();
            
            // Check if extension already exists
            const existingInput = document.querySelector(`input[value="${ext}"]`);
            if (existingInput) {
                alert('This extension already exists!');
                return;
            }
            
            // Create new extension checkbox with delete button
            const newExtDiv = document.createElement('div');
            newExtDiv.className = 'col-md-4 mb-2';
            newExtDiv.innerHTML = `
                <div class="form-check position-relative">
                    <input class="form-check-input extension-checkbox" type="checkbox" 
                           name="extensions[]" value="${ext}" 
                           id="ext_${ext.replace('.', '')}" checked>
                    <label class="form-check-label extension-label" for="ext_${ext.replace('.', '')}">
                        <span class="extension-badge available">${ext}</span>
                    </label>
                    <button type="button" class="btn btn-sm btn-outline-danger delete-extension" 
                            style="position: absolute; top: -5px; right: -5px; width: 20px; height: 20px; padding: 0; font-size: 10px; border-radius: 50%;"
                            title="Delete extension">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            // Insert before the Add Extensions button
            addMoreBtn.parentElement.parentElement.insertBefore(newExtDiv, addMoreBtn.parentElement);
            
            // Add delete functionality to the new button
            const deleteBtn = newExtDiv.querySelector('.delete-extension');
            deleteBtn.addEventListener('click', function() {
                if (confirm('Are you sure you want to remove this extension?')) {
                    newExtDiv.remove();
                }
            });
            
            extensionModal.hide();
        }
    });

    // Handle Enter key in modal input
    customExtensionInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            addExtensionBtn.click();
        }
    });

    // Add delete functionality to existing custom extensions
    document.querySelectorAll('.delete-extension').forEach(function(deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to remove this extension?')) {
                deleteBtn.closest('.col-md-4').remove();
            }
        });
    });

    // Duplicate check functionality
    checkDuplicateBtn.addEventListener('click', function() {
        const domainName = nameInput.value.trim();
        
        if (!domainName) {
            duplicateCheck.innerHTML = '<div class="alert alert-warning alert-sm">Please enter a domain name first.</div>';
            return;
        }

        // Don't check if it's the same as original name
        if (domainName === originalName) {
            duplicateCheck.innerHTML = '<div class="alert alert-info alert-sm"><i class="fas fa-info-circle me-1"></i>This is the current domain name.</div>';
            return;
        }

        // Show loading state
        checkDuplicateBtn.disabled = true;
        checkDuplicateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';

        // Make AJAX request to check for duplicates
        fetch('{{ route("domains.checkDuplicate") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ name: domainName })
        })
        .then(response => response.json())
        .then(data => {
            if (data.exists) {
                duplicateCheck.innerHTML = '<div class="alert alert-danger alert-sm"><i class="fas fa-exclamation-triangle me-1"></i>This domain name already exists!</div>';
                nameInput.classList.add('is-invalid');
            } else {
                duplicateCheck.innerHTML = '<div class="alert alert-success alert-sm"><i class="fas fa-check me-1"></i>Domain name is available!</div>';
                nameInput.classList.remove('is-invalid');
            }
        })
        .catch(error => {
            duplicateCheck.innerHTML = '<div class="alert alert-danger alert-sm">Error checking domain. Please try again.</div>';
        })
        .finally(() => {
            // Reset button state
            checkDuplicateBtn.disabled = false;
            checkDuplicateBtn.innerHTML = '<i class="fas fa-search"></i> Check';
        });
    });

    // Domain name cleaning functionality
    function cleanDomainName(value) {
        // Remove spaces and convert to lowercase
        return value.replace(/\s+/g, '').toLowerCase();
    }

    // Auto-clean domain name as user types
    nameInput.addEventListener('input', function() {
        const originalValue = this.value;
        const cleanedValue = cleanDomainName(originalValue);
        
        // Only update if the value changed to avoid cursor jumping
        if (originalValue !== cleanedValue) {
            const cursorPosition = this.selectionStart;
            this.value = cleanedValue;
            // Restore cursor position (accounting for removed characters)
            const removedChars = originalValue.length - cleanedValue.length;
            this.setSelectionRange(cursorPosition - removedChars, cursorPosition - removedChars);
        }
        
        if (nameInput.value.trim() !== originalName) {
            duplicateCheck.innerHTML = '';
            nameInput.classList.remove('is-invalid');
        }
    });
});
</script>
@endpush