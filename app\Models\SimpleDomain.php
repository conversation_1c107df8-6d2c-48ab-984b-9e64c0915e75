<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SimpleDomain extends Model
{
    protected $fillable = [
        'name',
        'categories'
    ];

    protected $casts = [
        'categories' => 'array'
    ];

    // Get category names with colors
    public function getCategoryNamesAttribute()
    {
        if (empty($this->categories)) {
            return [];
        }
        
        return Category::whereIn('id', $this->categories)->pluck('name')->toArray();
    }

    // Get categories with full details
    public function getCategoryDetailsAttribute()
    {
        if (empty($this->categories)) {
            return collect();
        }
        
        return Category::whereIn('id', $this->categories)->get();
    }

    // Scope for filtering by category
    public function scopeByCategory($query, $categoryId)
    {
        return $query->whereJsonContains('categories', (int)$categoryId);
    }

    // Scope for searching
    public function scopeSearch($query, $term)
    {
        return $query->where('name', 'like', '%' . $term . '%');
    }
}
