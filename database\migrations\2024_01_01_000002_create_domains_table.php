<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('domains', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->json('extensions'); // Array of extensions like ['.com', '.net']
            $table->json('categories'); // Array of category IDs
            $table->date('expiry_date');
            $table->tinyInteger('rating')->unsigned()->default(1); // 1-5 stars
            $table->boolean('is_available')->nullable(); // null = unknown, true/false = checked
            $table->timestamp('availability_checked_at')->nullable();
            $table->timestamps();
            
            $table->index(['expiry_date']);
            $table->index(['rating']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('domains');
    }
};