@extends('layouts.app')

@section('title', 'Domain Details - Domain CRM')

@push('styles')
<style>
.extension-badge {
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 0.9em;
    margin: 2px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.extension-badge.available {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.extension-badge.not-available {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.extension-badge .status-icon {
    font-size: 0.8em;
}

.domain-card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.star-rating .fas {
    color: #ffc107;
}

.star-rating .far {
    color: #dee2e6;
}

.extensions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 8px;
    margin-top: 8px;
}
</style>
@endpush

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-eye me-2 text-primary"></i>
        Domain Details
    </h1>
    <div>
        <a href="{{ route('domains.edit', $domain) }}" class="btn btn-warning me-2">
            <i class="fas fa-edit me-1"></i>
            Edit Domain
        </a>
        <a href="{{ route('domains.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>
            Back to Domains
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card domain-card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5 class="card-title mb-3">
                            <i class="fas fa-globe me-2"></i>
                            {{ $domain->name }}
                        </h5>
                        
                        <div class="mb-4">
                            <strong class="d-block mb-2">Extensions:</strong>
                            <div class="extensions-grid">
                                @php
                                    // Get domain's selected extensions
                                    $domainExtensions = $domain->extensions;
                                    
                                    // Define availability status for extensions
                                    $availableExtensions = ['.com', '.in'];
                                    
                                    // Debug: Check if extensions exist and what type they are
                                    $hasExtensions = !empty($domainExtensions) && is_array($domainExtensions);
                                @endphp
                                
                                @if($hasExtensions && count($domainExtensions) > 0)
                                    @foreach($domainExtensions as $ext)
                                        @php
                                            // Ensure extension starts with dot
                                            $extension = (string) $ext;
                                            if (!str_starts_with($extension, '.')) {
                                                $extension = '.' . $extension;
                                            }
                                            
                                            // All extensions added to domain are considered available (green)
                                            $isAvailable = true;
                                        @endphp
                                        <div class="extension-badge available">
                                            <i class="fas fa-check status-icon"></i>
                                            <span>{{ $extension }}</span>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        No extensions found for this domain.
                                        @if(!empty($domain->extensions))
                                            <br><small class="text-muted">Raw data: {{ json_encode($domain->extensions) }}</small>
                                        @endif
                                    </div>
                                @endif
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <strong class="d-block mb-2">Categories:</strong>
                            <div class="d-flex flex-wrap gap-2">
                                @if($domain->category_details && $domain->category_details->count() > 0)
                                    @foreach($domain->category_details as $category)
                                        <span class="badge fs-6 px-3 py-2" style="background-color: {{ $category->color }}; color: white;">
                                            <i class="fas fa-tag me-1"></i>
                                            {{ $category->name }}
                                        </span>
                                    @endforeach
                                @else
                                    <span class="text-muted">No categories assigned</span>
                                @endif
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <strong>Rating:</strong><br>
                            <div class="star-rating">
                                @for($i = 1; $i <= 5; $i++)
                                    <i class="fas fa-star{{ $i <= $domain->rating ? '' : '-o' }}"></i>
                                @endfor
                                <span class="ms-2">({{ $domain->rating }}/5)</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <strong>Expiry Date:</strong><br>
                            <span class="badge {{ $domain->days_left <= 7 ? 'bg-danger' : ($domain->days_left <= 30 ? 'bg-warning' : 'bg-success') }}">
                                {{ $domain->expiry_date->format('M d, Y') }}
                            </span>
                        </div>
                        
                        <div class="mb-3">
                            <strong>Days Left:</strong><br>
                            <span class="badge {{ $domain->days_left <= 7 ? 'bg-danger' : ($domain->days_left <= 30 ? 'bg-warning' : 'bg-success') }}">
                                {{ $domain->days_left }} days
                            </span>
                        </div>
                        
                        <div class="mb-3">
                            <strong>Availability:</strong><br>
                            <div class="d-flex align-items-center">
                                @if($domain->is_available !== null)
                                    <i class="fas fa-{{ $domain->is_available ? 'check text-success' : 'times text-danger' }} me-2"></i>
                                    {{ $domain->is_available ? 'Available' : 'Not Available' }}
                                @else
                                    <span class="text-muted">Unknown</span>
                                @endif
                                <button class="btn btn-sm btn-outline-primary ms-2" 
                                        onclick="checkAvailability('{{ $domain->name }}', {{ $domain->id }})">
                                    <i class="fas fa-sync-alt"></i>
                                    Check Now
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <strong>Created:</strong><br>
                            <small class="text-muted">{{ $domain->created_at->format('M d, Y H:i') }}</small>
                        </div>
                        
                        <div class="mb-3">
                            <strong>Last Updated:</strong><br>
                            <small class="text-muted">{{ $domain->updated_at->format('M d, Y H:i') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" 
                            onclick="checkAvailability('{{ $domain->name }}', {{ $domain->id }})">
                        <i class="fas fa-sync-alt me-1"></i>
                        Check Availability
                    </button>
                    
                    <a href="{{ route('domains.edit', $domain) }}" class="btn btn-outline-warning">
                        <i class="fas fa-edit me-1"></i>
                        Edit Domain
                    </a>
                    
                    <button class="btn btn-outline-danger" onclick="deleteDomain({{ $domain->id }})">
                        <i class="fas fa-trash me-1"></i>
                        Delete Domain
                    </button>
                </div>
            </div>
        </div>
        
        @if($domain->days_left <= 30)
        <div class="card mt-3">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Expiry Alert
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-0">This domain expires in {{ $domain->days_left }} days. Consider renewing it soon.</p>
            </div>
        </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
function checkAvailability(domainName, domainId) {
    const button = event.target.closest('button');
    const originalHtml = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
    button.disabled = true;
    
    $.post('{{ route("domains.check-availability") }}', {
        domain: domainName
    })
    .done(function(response) {
        // Update availability display
        const availabilityElements = document.querySelectorAll('[data-availability]');
        availabilityElements.forEach(element => {
            if (response.available) {
                element.innerHTML = `
                    <i class="fas fa-check text-success me-2"></i>
                    Available
                `;
            } else {
                element.innerHTML = `
                    <i class="fas fa-times text-danger me-2"></i>
                    Not Available
                `;
            }
        });
        
        // Show success message
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                Domain availability updated: ${response.available ? 'Available' : 'Not Available'}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('.container-fluid .p-4').prepend(alertHtml);
        
        // Auto-hide alert
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 3000);
    })
    .fail(function() {
        alert('Failed to check domain availability');
    })
    .always(function() {
        button.innerHTML = originalHtml;
        button.disabled = false;
    });
}

function deleteDomain(domainId) {
    if (confirm('Are you sure you want to delete this domain? This action cannot be undone.')) {
        $.ajax({
            url: `/domains/${domainId}`,
            type: 'DELETE',
            success: function() {
                window.location.href = '{{ route("domains.index") }}';
            },
            error: function() {
                alert('Failed to delete domain');
            }
        });
    }
}
</script>
@endpush