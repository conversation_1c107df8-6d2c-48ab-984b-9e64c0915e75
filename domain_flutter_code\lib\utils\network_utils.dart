import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:http/http.dart' as http;

class NetworkUtils {
  static final NetworkUtils _instance = NetworkUtils._internal();
  factory NetworkUtils() => _instance;
  NetworkUtils._internal();

  static const Duration _defaultTimeout = Duration(seconds: 10);

  /// Stream controller for network status changes
  final StreamController<bool> _networkStatusController =
      StreamController<bool>.broadcast();

  /// Stream of network status changes
  Stream<bool> get networkStatusStream => _networkStatusController.stream;

  bool _isListening = false;
  bool? _lastKnownStatus;

  /// Start listening to network changes
  void startNetworkMonitoring() {
    if (_isListening) return;

    _isListening = true;
    Connectivity().onConnectivityChanged.listen((
      List<ConnectivityResult> results,
    ) {
      _checkAndNotifyNetworkStatus();
    });

    // Initial check
    _checkAndNotifyNetworkStatus();
  }

  /// Stop listening to network changes
  void stopNetworkMonitoring() {
    _isListening = false;
  }

  /// Check network status and notify listeners
  Future<void> _checkAndNotifyNetworkStatus() async {
    try {
      final isConnected = await isConnectedToInternet();
      if (_lastKnownStatus != isConnected) {
        _lastKnownStatus = isConnected;
        _networkStatusController.add(isConnected);

        if (kDebugMode) {
          print(
            'Network status changed: ${isConnected ? "Connected" : "Disconnected"}',
          );
        }
      }
    } catch (e) {
      if (kDebugMode) print('Error checking network status: $e');
    }
  }

  /// Check if device has basic connectivity
  Future<bool> hasBasicConnectivity() async {
    try {
      final connectivityResults = await Connectivity().checkConnectivity();
      return connectivityResults.any(
        (result) => result != ConnectivityResult.none,
      );
    } catch (e) {
      if (kDebugMode) print('Basic connectivity check failed: $e');
      return false;
    }
  }

  /// Check if device is actually connected to the internet
  Future<bool> isConnectedToInternet({Duration? timeout}) async {
    timeout ??= const Duration(seconds: 5); // Shorter timeout for better UX

    try {
      // First check basic connectivity
      if (!await hasBasicConnectivity()) {
        return false;
      }

      // Test with a simple, reliable endpoint first
      try {
        final response = await http
            .head(Uri.parse('https://www.google.com'))
            .timeout(timeout);

        if (response.statusCode >= 200 && response.statusCode < 500) {
          return true;
        }
      } catch (e) {
        if (kDebugMode) print('Google connectivity test failed: $e');
      }

      // Fallback to DNS servers if Google fails
      try {
        final response = await http
            .head(Uri.parse('https://dns.google'))
            .timeout(timeout);

        if (response.statusCode >= 200 && response.statusCode < 500) {
          return true;
        }
      } catch (e) {
        if (kDebugMode) print('DNS Google test failed: $e');
      }

      // Final fallback - assume connected if basic connectivity exists
      // This prevents false negatives in restrictive networks
      return await hasBasicConnectivity();
    } catch (e) {
      if (kDebugMode) print('Internet connectivity check failed: $e');
      // If all tests fail but we have basic connectivity, assume connected
      return await hasBasicConnectivity();
    }
  }

  /// Test connectivity to a specific host and port
  Future<bool> canReachHost(String host, int port, {Duration? timeout}) async {
    timeout ??= _defaultTimeout;

    try {
      final socket = await Socket.connect(host, port, timeout: timeout);
      socket.destroy();
      return true;
    } catch (e) {
      if (kDebugMode) print('Cannot reach $host:$port - $e');
      return false;
    }
  }

  /// Get detailed network information
  Future<Map<String, dynamic>> getNetworkInfo() async {
    try {
      final connectivityResults = await Connectivity().checkConnectivity();
      final hasBasic = await hasBasicConnectivity();
      final hasInternet = await isConnectedToInternet();

      return {
        'connectivity_results': connectivityResults.map((e) => e.name).toList(),
        'has_basic_connectivity': hasBasic,
        'has_internet_access': hasInternet,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Get user-friendly network status message
  Future<String> getNetworkStatusMessage() async {
    try {
      final hasBasic = await hasBasicConnectivity();
      if (!hasBasic) {
        return 'No network connection detected. Please check your WiFi or mobile data.';
      }

      // If we have basic connectivity, assume internet is available
      // This prevents false negatives in corporate networks, VPNs, etc.
      return 'Connected to internet';
    } catch (e) {
      return 'Unable to determine network status. Please check your connection.';
    }
  }

  /// Dispose resources
  void dispose() {
    _networkStatusController.close();
    _isListening = false;
  }
}

/// Extension to add network utilities to BuildContext
extension NetworkUtilsExtension on NetworkUtils {
  /// Wait for internet connection with timeout
  Future<bool> waitForConnection({
    Duration timeout = const Duration(seconds: 30),
    Duration checkInterval = const Duration(seconds: 2),
  }) async {
    final endTime = DateTime.now().add(timeout);

    while (DateTime.now().isBefore(endTime)) {
      if (await isConnectedToInternet()) {
        return true;
      }
      await Future.delayed(checkInterval);
    }

    return false;
  }

  /// Retry a function until network is available
  Future<T> retryWithNetwork<T>(
    Future<T> Function() function, {
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 2),
  }) async {
    for (int i = 0; i < maxRetries; i++) {
      try {
        if (await isConnectedToInternet()) {
          return await function();
        }
      } catch (e) {
        if (i == maxRetries - 1) rethrow;
      }

      await Future.delayed(retryDelay);
    }

    throw Exception('Failed to execute function after $maxRetries retries');
  }
}
