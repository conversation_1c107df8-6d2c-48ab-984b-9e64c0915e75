class SimpleDomain {
  final int id;
  final String name;
  final List<int> categories;
  final String createdAt;
  final String updatedAt;
  final List<String>? categoryNames;

  SimpleDomain({
    required this.id,
    required this.name,
    required this.categories,
    required this.createdAt,
    required this.updatedAt,
    this.categoryNames,
  });

  factory SimpleDomain.fromJson(Map<String, dynamic> json) {
    return SimpleDomain(
      id: _parseInt(json['id']),
      name: json['name']?.toString() ?? '',
      categories: _parseIntList(json['categories']),
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
      categoryNames: json['category_names'] != null 
          ? _parseStringList(json['category_names'])
          : null,
    );
  }

  static int _parseInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  static List<String> _parseStringList(dynamic value) {
    if (value == null) return [];
    if (value is List) return value.map((e) => e.toString()).toList();
    return [];
  }

  static List<int> _parseIntList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.map((e) {
        if (e is int) return e;
        if (e is String) return int.tryParse(e) ?? 0;
        return 0;
      }).toList();
    }
    return [];
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'categories': categories,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'category_names': categoryNames,
    };
  }
}