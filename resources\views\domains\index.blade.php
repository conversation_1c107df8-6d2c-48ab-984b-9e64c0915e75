@extends('layouts.app')

@section('title', 'Domains - Domain CRM')

@push('styles')
<style>
/* Extension Badges */
.extensions-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.extension-badge {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 1.1rem;
    font-weight: 700;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
    margin-bottom: 4px;
    position: relative;
    overflow: hidden;
    color: #16a34a !important;
}

.extension-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.extension-badge i {
    font-size: 0.9rem;
}

/* Specific extension colors - Light pastel versions with green text */
.extension-badge[data-ext="com"] {
    background: linear-gradient(135deg, #a8b5ff 0%, #c4b5fd 100%);
}

.extension-badge[data-ext="net"] {
    background: linear-gradient(135deg, #fbb6ce 0%, #f9a8d4 100%);
}

.extension-badge[data-ext="org"] {
    background: linear-gradient(135deg, #7dd3fc 0%, #a5f3fc 100%);
}

.extension-badge[data-ext="io"] {
    background: linear-gradient(135deg, #86efac 0%, #6ee7b7 100%);
}

.extension-badge[data-ext="co"] {
    background: linear-gradient(135deg, #fda4af 0%, #fed7aa 100%);
}

.extension-badge[data-ext="app"] {
    background: linear-gradient(135deg, #bfdbfe 0%, #ddd6fe 100%);
}

.extension-badge[data-ext="dev"] {
    background: linear-gradient(135deg, #fecaca 0%, #fed7d7 100%);
}

.extension-badge[data-ext="tech"] {
    background: linear-gradient(135deg, #c7d2fe 0%, #ddd6fe 100%);
}

.extension-badge[data-ext="info"] {
    background: linear-gradient(135deg, #bae6fd 0%, #a5f3fc 100%);
}

.extension-badge[data-ext="biz"] {
    background: linear-gradient(135deg, #fed7aa 0%, #fde68a 100%);
}

.extension-badge[data-ext="me"] {
    background: linear-gradient(135deg, #f3e8ff 0%, #ede9fe 100%);
}

/* Default light gradient for other extensions */
.extension-badge:not([data-ext]) {
    background: linear-gradient(135deg, #e0e7ff 0%, #f3f4f6 100%);
}

/* New Extension Badges with Availability Status */
.extension-badge-new {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 0.85em;
    margin: 2px;
    gap: 4px;
}

.extension-badge-new.available {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.extension-badge-new.not-available {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.extension-badge-new .fas {
    font-size: 0.75em;
}

.extension-badge-new.selected {
    border-width: 2px;
    box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.25);
    font-weight: 600;
}

/* Category Badges */
.categories-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.category-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.2s ease;
    margin-bottom: 4px;
    position: relative;
    overflow: hidden;
}

.category-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.category-badge i {
    font-size: 0.75rem;
}

/* Enhanced table styling */
.table td {
    vertical-align: middle;
    padding: 16px 12px;
}

.table th {
    font-weight: 600;
    color: #374151;
    background-color: #f8fafc;
    border-bottom: 2px solid #e5e7eb;
    padding: 16px 12px;
}

.table-hover tbody tr:hover {
    background-color: #f8fafc;
    transform: scale(1.001);
    transition: all 0.2s ease;
}

/* Star rating enhancement */
.star-rating {
    color: #fbbf24;
    font-size: 1.1rem;
}

.star-rating i {
    margin-right: 2px;
    transition: color 0.2s ease;
}

/* Small star rating for table */
.star-rating-small {
    color: #fbbf24;
    font-size: 0.85rem;
}

.star-rating-small i {
    margin-right: 1px;
    transition: color 0.2s ease;
}

/* Days left badge enhancement */
.badge {
    font-size: 0.85rem;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: 500;
}

/* Action buttons enhancement */
.btn-group .btn {
    border-radius: 6px;
    margin-right: 2px;
    transition: all 0.2s ease;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .extensions-container,
    .categories-container {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .extension-badge,
    .category-badge {
        margin-bottom: 2px;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* Loading state for availability check */
.btn .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced card styling */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #e5e7eb;
    border-radius: 12px 12px 0 0 !important;
}

/* Filter form enhancements */
.form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Empty state styling */
.text-center.py-5 {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 12px;
    margin: 20px 0;
}
</style>
@endpush

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-list me-2 text-primary"></i>
        Domain Management
    </h1>
    <a href="{{ route('domains.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        Add New Domain
    </a>
</div>

<!-- A-Z Alphabetical Filter -->
<div class="card mb-3">
    <div class="card-body py-3">
        <div class="d-flex flex-wrap align-items-center">
            <span class="me-3 fw-bold text-muted">Filter by Letter:</span>
            <div class="d-flex flex-wrap gap-1">
                @foreach(range('A', 'Z') as $letter)
                    <a href="{{ route('domains.index', array_merge(request()->all(), ['letter' => $letter])) }}" 
                       class="btn btn-sm {{ request('letter') == $letter ? 'btn-primary' : 'btn-outline-primary' }} letter-filter">
                        {{ $letter }}
                    </a>
                @endforeach
                @if(request('letter'))
                    <a href="{{ route('domains.index', request()->except('letter')) }}" 
                       class="btn btn-sm btn-outline-secondary ms-2">
                        <i class="fas fa-times"></i> Clear
                    </a>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Advanced Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            Advanced Filters
        </h5>
    </div>
    <div class="card-body">
        <form id="filterForm" method="GET">
            <input type="hidden" name="letter" value="{{ request('letter') }}">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">Domain Name</label>
                    <input type="text" class="form-control" name="domain_name" 
                           value="{{ request('domain_name') }}" placeholder="Search domains...">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Category</label>
                    <select class="form-select" name="category_id">
                        <option value="">All Categories</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" 
                                    {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Rating</label>
                    <select class="form-select" name="rating">
                        <option value="">All Ratings</option>
                        @for($i = 5; $i >= 1; $i--)
                            <option value="{{ $i }}" {{ request('rating') == $i ? 'selected' : '' }}>
                                {{ $i }} Star{{ $i > 1 ? 's' : '' }}
                            </option>
                        @endfor
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Expiry From</label>
                    <input type="date" class="form-control" name="expiry_from" 
                           value="{{ request('expiry_from') }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Expiry To</label>
                    <input type="date" class="form-control" name="expiry_to" 
                           value="{{ request('expiry_to') }}">
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        Apply Filters
                    </button>
                    <a href="{{ route('domains.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        Clear Filters
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Domains Table -->
<div class="card">
    <div class="card-body">
        @if($domains->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Domain Name</th>
                            <th>Extensions</th>
                            <th>Categories</th>
                            <th>Expiry Date</th>
                            <th>Days Left</th>
                            <th>Rating</th>
                            <th>Availability</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($domains as $domain)
                            <tr>
                                <td>
                                    <strong>{{ ucfirst($domain->name) }}</strong>
                                </td>
                                <td>
                                    <div class="extensions-container">
                                        @php
                                            // Always show .com and .in extensions
                                            $standardExtensions = ['.com', '.in'];
                                            
                                            // Get domain's selected extensions
                                            $domainExtensions = $domain->extensions ?? [];
                                            
                                            // Collect custom extensions
                                            $customExtensions = [];
                                            foreach($domainExtensions as $ext) {
                                                $extension = (string) $ext;
                                                if (!str_starts_with($extension, '.')) {
                                                    $extension = '.' . $extension;
                                                }
                                                
                                                // If it's not .com or .in, it's a custom extension
                                                if (!in_array($extension, $standardExtensions)) {
                                                    $customExtensions[] = $extension;
                                                }
                                            }
                                        @endphp
                                        
                                        <!-- Show .com and .in with availability status -->
                                        @foreach($standardExtensions as $ext)
                                            @php
                                                // Check if this extension is selected for this domain
                                                $isSelected = in_array($ext, $domainExtensions) || in_array(ltrim($ext, '.'), $domainExtensions);
                                            @endphp
                                            <span class="extension-badge-new {{ $isSelected ? 'available' : 'not-available' }}">
                                                @if($isSelected)
                                                    <i class="fas fa-check me-1"></i>
                                                @else
                                                    <i class="fas fa-times me-1"></i>
                                                @endif
                                                {{ $ext }}
                                            </span>
                                        @endforeach
                                        
                                        <!-- Show custom extensions (always green) -->
                                        @foreach($customExtensions as $ext)
                                            <span class="extension-badge-new available">
                                                <i class="fas fa-check me-1"></i>
                                                {{ $ext }}
                                            </span>
                                        @endforeach
                                    </div>
                                </td>
                                <td>
                                    <div class="categories-container">
                                        @foreach($domain->category_details as $category)
                                            <span class="category-badge" style="background-color: {{ $category->color }}20; border-left: 3px solid {{ $category->color }}; color: {{ $category->color }};">
                                                <i class="fas fa-tag me-1"></i>
                                                {{ $category->name }}
                                            </span>
                                        @endforeach
                                    </div>
                                </td>
                                <td>{{ $domain->expiry_date->format('M d, Y') }}</td>
                                <td>
                                    <span class="badge {{ $domain->days_left <= 7 ? 'bg-danger' : ($domain->days_left <= 30 ? 'bg-warning' : 'bg-success') }}">
                                        {{ $domain->days_left }} days
                                    </span>
                                </td>
                                <td>
                                    <div class="star-rating-small d-flex align-items-center">
                                        @for($i = 1; $i <= 5; $i++)
                                            <i class="fas fa-star{{ $i <= $domain->rating ? '' : '-o' }}"></i>
                                        @endfor
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        @if($domain->is_available !== null)
                                            <i class="fas fa-{{ $domain->is_available ? 'check text-success' : 'times text-danger' }} me-2"></i>
                                            {{ $domain->is_available ? 'Available' : 'Not Available' }}
                                        @else
                                            <span class="text-muted">Unknown</span>
                                        @endif
                                        <button class="btn btn-sm btn-outline-primary ms-2" 
                                                onclick="checkAvailability('{{ $domain->name }}', {{ $domain->id }})">
                                            <i class="fas fa-sync-alt"></i>
                                        </button>
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('domains.show', $domain) }}" 
                                           class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('domains.edit', $domain) }}" 
                                           class="btn btn-sm btn-outline-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteDomain({{ $domain->id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="mt-4">
                {{ $domains->links('pagination.custom') }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-globe fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No domains found</h5>
                <p class="text-muted">Start by adding your first domain to the system.</p>
                <a href="{{ route('domains.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    Add First Domain
                </a>
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
function checkAvailability(domainName, domainId) {
    const button = event.target.closest('button');
    const originalHtml = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;
    
    $.post('{{ route("domains.check-availability") }}', {
        domain: domainName
    })
    .done(function(response) {
        const row = button.closest('tr');
        const availabilityCell = row.querySelector('td:nth-child(7)');
        
        if (response.available) {
            availabilityCell.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-check text-success me-2"></i>
                    Available
                    <button class="btn btn-sm btn-outline-primary ms-2" 
                            onclick="checkAvailability('${domainName}', ${domainId})">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            `;
        } else {
            availabilityCell.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-times text-danger me-2"></i>
                    Not Available
                    <button class="btn btn-sm btn-outline-primary ms-2" 
                            onclick="checkAvailability('${domainName}', ${domainId})">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            `;
        }
    })
    .fail(function() {
        alert('Failed to check domain availability');
    })
    .always(function() {
        button.innerHTML = originalHtml;
        button.disabled = false;
    });
}

function deleteDomain(domainId) {
    if (confirm('Are you sure you want to delete this domain?')) {
        $.ajax({
            url: `/domains/${domainId}`,
            type: 'DELETE',
            success: function() {
                location.reload();
            },
            error: function() {
                alert('Failed to delete domain');
            }
        });
    }
}
</script>
@endpush