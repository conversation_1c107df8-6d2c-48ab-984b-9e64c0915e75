import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/admin.dart';
import '../services/simple_api_service.dart';
import '../services/direct_api_service.dart';
import '../services/ultra_simple_api.dart';
import '../utils/constants.dart';
import '../utils/network_utils.dart';
import '../utils/error_handler.dart';
import 'dart:convert';
import 'dart:async';

class AuthProvider with ChangeNotifier {
  Admin? _admin;
  bool _isLoading = false;
  String? _error;
  bool _isConnecting = false;

  final SimpleApiService _apiService = SimpleApiService();
  final NetworkUtils _networkUtils = NetworkUtils();
  StreamSubscription<bool>? _networkSubscription;

  Admin? get admin => _admin;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _admin != null;
  bool get isConnecting => _isConnecting;

  /// Initialize network monitoring
  void initializeNetworkMonitoring() {
    _networkUtils.startNetworkMonitoring();
    _networkSubscription = _networkUtils.networkStatusStream.listen((
      isConnected,
    ) {
      if (!isConnected && _error == null) {
        _setError(
          'Network connection lost. Please check your internet connection.',
        );
      } else if (isConnected &&
          _error?.contains('Network connection lost') == true) {
        _clearError();
      }
    });
  }

  Future<void> checkAuthStatus() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(StorageKeys.authToken);
      final userData = prefs.getString(StorageKeys.userData);

      if (token != null && userData != null) {
        _admin = Admin.fromJson(json.decode(userData));

        // Verify token is still valid
        try {
          await _apiService.getUser();
        } catch (e) {
          // Token is invalid, clear stored data
          await logout();
        }
      }
    } catch (e) {
      _setError('Failed to check authentication status');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      // Validate input
      if (email.trim().isEmpty || password.isEmpty) {
        _setError('Email and password are required');
        return false;
      }

      // Validate email format
      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email.trim())) {
        _setError('Please enter a valid email address');
        return false;
      }

      // Try multiple API approaches for maximum compatibility
      Map<String, dynamic> response;

      try {
        response = await DirectApiService.directLogin(email.trim(), password);
      } catch (e) {
        try {
          response = await UltraSimpleApi.ultraSimpleLogin(
            email.trim(),
            password,
          );
        } catch (e2) {
          // Last resort: try original API service
          response = await _apiService.login(email.trim(), password);
        }
      }

      // Check if response contains required fields
      if (response.containsKey('success') && response['success'] == true) {
        final token = response['token'];
        final adminData = response['admin'];

        if (token == null || adminData == null) {
          _setError(
            'Invalid response from server - missing token or admin data',
          );
          return false;
        }

        try {
          _admin = Admin.fromJson(adminData);
          if (kDebugMode) {
            print(
              'AuthProvider: Admin object created successfully: ${_admin?.name}',
            );
          }
        } catch (e) {
          _setError('Invalid user data received from server');
          return false;
        }

        // Store token and user data
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(StorageKeys.authToken, token);
        await prefs.setString(StorageKeys.userData, json.encode(adminData));

        notifyListeners();
        return true;
      } else {
        // Handle case where success is not true or missing
        final message =
            response['message'] ?? 'Login failed - invalid credentials';
        _setError(message);
        return false;
      }
    } catch (e) {
      // Log error for debugging
      ErrorHandler.logError(e, context: 'AuthProvider.login');

      // Get user-friendly error message
      final errorMessage = ErrorHandler.getErrorMessage(e);
      _setError(errorMessage);
      return false;
    } finally {
      _isConnecting = false;
      _setLoading(false);
    }
  }

  Future<void> logout() async {
    _setLoading(true);

    try {
      if (_admin != null) {
        await _apiService.logout();
      }
    } catch (e) {
      // Continue with logout even if API call fails
    }

    // Clear stored data
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(StorageKeys.authToken);
    await prefs.remove(StorageKeys.userData);

    _admin = null;
    _setLoading(false);
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  /// Clean up resources
  @override
  void dispose() {
    _networkSubscription?.cancel();
    _networkUtils.dispose();
    super.dispose();
  }
}
