<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Admin;
use App\Models\Domain;
use App\Models\Category;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

class AdminController extends Controller
{
    public function dashboard()
    {
        $stats = [
            'total_domains' => Domain::count(),
            'total_categories' => Category::count(),
            'expiring_domains' => Domain::where('expiry_date', '<=', now()->addDays(30))->count(),
            'recent_domains' => Domain::latest()->take(5)->get(),
        ];

        return view('admin.dashboard', compact('stats'));
    }

    public function settings()
    {
        $admin = Auth::guard('admin')->user();
        return view('admin.settings', compact('admin'));
    }

    public function updateSettings(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:admins,email,' . Auth::guard('admin')->id(),
            'current_password' => 'nullable|required_with:new_password',
            'new_password' => 'nullable|min:6|confirmed',
        ]);

        $admin = Auth::guard('admin')->user();

        // Update basic info
        $admin->name = $request->name;
        $admin->email = $request->email;

        // Update password if provided
        if ($request->filled('new_password')) {
            if (!Hash::check($request->current_password, $admin->password)) {
                return back()->withErrors(['current_password' => 'Current password is incorrect.']);
            }
            $admin->password = Hash::make($request->new_password);
        }

        $admin->save();

        return redirect()->route('admin.settings')->with('success', 'Settings updated successfully!');
    }

    public function systemSettings()
    {
        $settings = [
            'app_name' => config('app.name', 'Domain CRM'),
            'app_url' => config('app.url'),
            'mail_driver' => config('mail.default'),
            'timezone' => config('app.timezone'),
        ];

        return view('admin.system-settings', compact('settings'));
    }

    public function updateSystemSettings(Request $request)
    {
        $request->validate([
            'app_name' => 'required|string|max:255',
            'app_url' => 'required|url',
            'timezone' => 'required|string',
        ]);

        // In a real application, you would update these in a settings table
        // For now, we'll just show success message
        return redirect()->route('admin.system-settings')->with('success', 'System settings updated successfully!');
    }
}