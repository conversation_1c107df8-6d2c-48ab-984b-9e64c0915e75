<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Domain;
use App\Models\SimpleDomain;
use App\Models\Category;
use App\Rules\DomainNameRule;
use Illuminate\Http\Request;

class DomainApiController extends Controller
{
    public function index(Request $request)
    {
        $query = Domain::query();

        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        if ($request->filled('category_id')) {
            $query->byCategory($request->category_id);
        }

        if ($request->filled('rating')) {
            $query->byRating($request->rating);
        }

        if ($request->filled('extensions')) {
            $extensions = explode(',', $request->extensions);
            $query->where(function($q) use ($extensions) {
                foreach ($extensions as $extension) {
                    $q->orWhereJsonContains('extensions', trim($extension));
                }
            });
        }

        $domains = $query->orderBy('created_at', 'desc')->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $domains->items(),
            'pagination' => [
                'current_page' => $domains->currentPage(),
                'last_page' => $domains->lastPage(),
                'total' => $domains->total(),
                'per_page' => $domains->perPage(),
            ]
        ]);
    }

    public function store(Request $request)
    {
        $request->merge([
            'name' => $this->cleanDomainName($request->name)
        ]);

        $request->validate([
            'name' => ['required', 'string', new DomainNameRule, 'unique:domains,name'],
            'extensions' => 'required|array|min:1',
            'categories' => 'required|array|min:1',
            'expiry_date' => 'required|date',
            'rating' => 'required|integer|min:1|max:5'
        ]);

        $domain = Domain::create($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Domain created successfully',
            'data' => $domain
        ], 201);
    }

    public function show(Domain $domain)
    {
        return response()->json([
            'success' => true,
            'data' => $domain
        ]);
    }

    public function update(Request $request, Domain $domain)
    {
        $request->merge([
            'name' => $this->cleanDomainName($request->name)
        ]);

        $request->validate([
            'name' => ['required', 'string', new DomainNameRule, 'unique:domains,name,' . $domain->id],
            'extensions' => 'required|array|min:1',
            'categories' => 'required|array|min:1',
            'expiry_date' => 'required|date',
            'rating' => 'required|integer|min:1|max:5'
        ]);

        $domain->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Domain updated successfully',
            'data' => $domain
        ]);
    }

    public function destroy(Domain $domain)
    {
        $domain->delete();

        return response()->json([
            'success' => true,
            'message' => 'Domain deleted successfully'
        ]);
    }

    public function checkAvailability(Request $request)
    {
        $request->validate([
            'domain' => 'required|string'
        ]);

        $isAvailable = rand(0, 1) === 1;
        
        $domain = Domain::where('name', $request->domain)->first();
        if ($domain) {
            $domain->update(['is_available' => $isAvailable]);
        }

        return response()->json([
            'success' => true,
            'available' => $isAvailable,
            'domain' => $request->domain
        ]);
    }

    public function checkDuplicate(Request $request)
    {
        $exists = Domain::where('name', $request->name)->exists();
        
        return response()->json([
            'success' => true,
            'exists' => $exists
        ]);
    }

    public function getDashboardStats()
    {
        $totalDomains = Domain::count();
        $totalSimpleDomains = SimpleDomain::count();
        $totalCategories = Category::count();
        $expiringSoon = Domain::expiringSoon(30)->count();
        $topRated = Domain::topRated(4)->count();
        $available = Domain::available()->count();

        return response()->json([
            'success' => true,
            'data' => [
                'total_domains' => $totalDomains,
                'total_simple_domains' => $totalSimpleDomains,
                'total_categories' => $totalCategories,
                'expiring_soon' => $expiringSoon,
                'top_rated' => $topRated,
                'available' => $available,
            ]
        ]);
    }

    private function cleanDomainName($domainName)
    {
        if (!$domainName) {
            return $domainName;
        }
        
        return strtolower(str_replace(' ', '', trim($domainName)));
    }
}