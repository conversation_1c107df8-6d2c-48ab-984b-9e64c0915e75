@extends('layouts.app')

@section('title', 'Buy Domain - Domain CRM')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-shopping-cart me-2 text-success"></i>
        Buy Domain: {{ $simpleDomain->name }}
    </h1>
    <a href="{{ route('simple-domains.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>
        Back to Reserve Domains
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Complete Domain Purchase
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-lightbulb me-2"></i>
                    Complete all the information below to finalize your domain purchase. You can edit the domain name and categories if needed.
                </div>

                <form id="buyDomainForm" method="POST" action="{{ route('domains.store') }}">
                    @csrf
                    
                    <!-- Hidden field to track simple domain -->
                    <input type="hidden" name="from_simple_domain" value="{{ $simpleDomain->id }}">
                    
                    <!-- Domain Name (Editable) -->
                    <div class="mb-3">
                        <label for="name" class="form-label">Domain Name *</label>
                        <div class="input-group">
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $simpleDomain->name) }}" 
                                   placeholder="example" required>
                            <button type="button" class="btn btn-outline-primary" id="checkDuplicateBtn">
                                <i class="fas fa-search"></i>
                                Check
                            </button>
                        </div>
                        <div id="duplicateCheck" class="mt-2"></div>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Categories (Editable) -->
                    <div class="mb-3">
                        <label class="form-label">Categories *</label>
                        <div class="row">
                            @php
                                $oldCategories = old('categories', $simpleDomain->categories);
                            @endphp
                            @foreach($categories as $category)
                                <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="categories[]" value="{{ $category->id }}" 
                                               id="cat{{ $category->id }}"
                                               {{ in_array($category->id, $oldCategories) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="cat{{ $category->id }}">
                                            <span class="badge" style="background-color: {{ $category->color }}">
                                                {{ $category->name }}
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        @error('categories')
                            <div class="text-danger small">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Extensions -->
                    <div class="mb-3">
                        <label class="form-label">Extensions *</label>
                        <div id="extensionsContainer">
                            <!-- Default Extensions (.in and .com) -->
                            <div class="row" id="defaultExtensions">
                                @php
                                    $defaultExtensions = ['.in', '.com'];
                                    $oldExtensions = old('extensions', []);
                                @endphp
                                @foreach($defaultExtensions as $ext)
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input extension-checkbox" type="checkbox" 
                                                   name="extensions[]" value="{{ $ext }}" 
                                                   id="ext_{{ str_replace('.', '', $ext) }}"
                                                   {{ in_array($ext, $oldExtensions) ? 'checked' : '' }}>
                                            <label class="form-check-label extension-label" for="ext_{{ str_replace('.', '', $ext) }}">
                                                <span class="extension-badge available">{{ $ext }}</span>
                                            </label>
                                        </div>
                                    </div>
                                @endforeach
                                
                                <!-- Add Extensions Button -->
                                <div class="col-md-4 mb-2">
                                    <button type="button" class="btn btn-outline-primary btn-sm" id="addMoreExtensions">
                                        <i class="fas fa-plus me-1"></i>
                                        Add Extensions
                                    </button>
                                </div>
                            </div>
                        </div>
                        @error('extensions')
                            <div class="text-danger small">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Expiry Date -->
                    <div class="mb-3">
                        <label for="expiry_date" class="form-label">Expiry Date *</label>
                        <input type="date" class="form-control @error('expiry_date') is-invalid @enderror" 
                               id="expiry_date" name="expiry_date" value="{{ old('expiry_date') }}" 
                               min="{{ date('Y-m-d', strtotime('+1 day')) }}" required>
                        @error('expiry_date')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Rating -->
                    <div class="mb-4">
                        <label for="rating" class="form-label">
                            <i class="fas fa-star text-warning me-1"></i>
                            Rating *
                        </label>
                        <div class="rating-container">
                            <select class="form-select @error('rating') is-invalid @enderror" 
                                    id="rating" name="rating" required>
                                <option value="">Select Rating</option>
                                @for($i = 1; $i <= 5; $i++)
                                    <option value="{{ $i }}" {{ old('rating') == $i ? 'selected' : '' }}>
                                        @for($j = 1; $j <= $i; $j++)★@endfor
                                        @for($j = $i + 1; $j <= 5; $j++)☆@endfor
                                        ({{ $i }} Star{{ $i > 1 ? 's' : '' }})
                                    </option>
                                @endfor
                            </select>
                            <div class="star-rating mt-2" id="starRating">
                                @for($i = 1; $i <= 5; $i++)
                                    <i class="fas fa-star star" data-rating="{{ $i }}" style="color: #ddd; cursor: pointer; font-size: 1.2em;"></i>
                                @endfor
                            </div>
                        </div>
                        @error('rating')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ route('simple-domains.index') }}" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-shopping-cart me-1"></i>
                            Complete Purchase
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Extension Modal -->
<div class="modal fade" id="extensionModal" tabindex="-1" aria-labelledby="extensionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="extensionModalLabel">
                    <i class="fas fa-plus me-2"></i>Add Custom Extension
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="customExtension" class="form-label">Extension Name</label>
                    <div class="input-group">
                        <span class="input-group-text">.</span>
                        <input type="text" class="form-control" id="customExtension" placeholder="net, org, co" autocomplete="off">
                    </div>
                    <div class="form-text">Enter extension without the dot (e.g., net, org, co)</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="addExtensionBtn">
                    <i class="fas fa-plus me-1"></i>Add Extension
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.extension-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 0.9em;
}

.extension-badge.available {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.extension-badge.not-available {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.extension-checkbox:checked + .extension-label .extension-badge {
    font-weight: 600;
    box-shadow: 0 0 0 2px rgba(0,123,255,.25);
}

#addMoreExtensions {
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const addMoreBtn = document.getElementById('addMoreExtensions');
    const checkDuplicateBtn = document.getElementById('checkDuplicateBtn');

    // Modal elements
    const extensionModal = new bootstrap.Modal(document.getElementById('extensionModal'));
    const customExtensionInput = document.getElementById('customExtension');
    const addExtensionBtn = document.getElementById('addExtensionBtn');

    // Add Extensions functionality - Show modal
    addMoreBtn.addEventListener('click', function() {
        customExtensionInput.value = '';
        extensionModal.show();
        setTimeout(() => customExtensionInput.focus(), 300);
    });

    // Handle modal add extension button
    addExtensionBtn.addEventListener('click', function() {
        const customExtension = customExtensionInput.value.trim();
        if (customExtension) {
            let ext = '.' + customExtension.toLowerCase();
            
            // Check if extension already exists
            const existingInput = document.querySelector(`input[value="${ext}"]`);
            if (existingInput) {
                alert('This extension already exists!');
                return;
            }
            
            // Create new extension checkbox with delete button
            const newExtDiv = document.createElement('div');
            newExtDiv.className = 'col-md-4 mb-2';
            newExtDiv.innerHTML = `
                <div class="form-check position-relative">
                    <input class="form-check-input extension-checkbox" type="checkbox" 
                           name="extensions[]" value="${ext}" 
                           id="ext_${ext.replace('.', '')}" checked>
                    <label class="form-check-label extension-label" for="ext_${ext.replace('.', '')}">
                        <span class="extension-badge available">${ext}</span>
                    </label>
                    <button type="button" class="btn btn-sm btn-outline-danger delete-extension" 
                            style="position: absolute; top: -5px; right: -5px; width: 20px; height: 20px; padding: 0; font-size: 10px; border-radius: 50%;"
                            title="Delete extension">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            // Insert before the Add Extensions button
            addMoreBtn.parentElement.parentElement.insertBefore(newExtDiv, addMoreBtn.parentElement);
            
            // Add delete functionality to the new button
            const deleteBtn = newExtDiv.querySelector('.delete-extension');
            deleteBtn.addEventListener('click', function() {
                if (confirm('Are you sure you want to remove this extension?')) {
                    newExtDiv.remove();
                }
            });
            
            extensionModal.hide();
        }
    });

    // Handle Enter key in modal input
    customExtensionInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            addExtensionBtn.click();
        }
    });
    const nameInput = document.getElementById('name');
    const duplicateCheck = document.getElementById('duplicateCheck');
    const ratingSelect = document.getElementById('rating');
    const starRating = document.getElementById('starRating');
    const stars = starRating.querySelectorAll('.star');

    // Duplicate check functionality
    checkDuplicateBtn.addEventListener('click', function() {
        const domainName = nameInput.value.trim();
        
        if (!domainName) {
            duplicateCheck.innerHTML = '<div class="alert alert-warning alert-sm">Please enter a domain name first.</div>';
            return;
        }

        // Show loading state
        checkDuplicateBtn.disabled = true;
        checkDuplicateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';

        // Get CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (!csrfToken) {
            duplicateCheck.innerHTML = '<div class="alert alert-danger alert-sm">CSRF token not found. Please refresh the page.</div>';
            checkDuplicateBtn.disabled = false;
            checkDuplicateBtn.innerHTML = '<i class="fas fa-search"></i> Check';
            return;
        }

        // Make AJAX request to check for duplicates in main domains table
        fetch('/domains/check-duplicate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
                'Accept': 'application/json'
            },
            body: JSON.stringify({ name: domainName })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.exists) {
                duplicateCheck.innerHTML = '<div class="alert alert-danger alert-sm"><i class="fas fa-exclamation-triangle me-1"></i>This domain name already exists in Buy Domains!</div>';
                nameInput.classList.add('is-invalid');
            } else {
                duplicateCheck.innerHTML = '<div class="alert alert-success alert-sm"><i class="fas fa-check me-1"></i>Domain name is available!</div>';
                nameInput.classList.remove('is-invalid');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            duplicateCheck.innerHTML = '<div class="alert alert-danger alert-sm">Error checking domain. Please try again.</div>';
        })
        .finally(() => {
            // Reset button state
            checkDuplicateBtn.disabled = false;
            checkDuplicateBtn.innerHTML = '<i class="fas fa-search"></i> Check';
        });
    });

    // Domain name cleaning functionality
    function cleanDomainName(value) {
        // Remove spaces and convert to lowercase
        return value.replace(/\s+/g, '').toLowerCase();
    }

    // Auto-clean domain name as user types
    nameInput.addEventListener('input', function() {
        const originalValue = this.value;
        const cleanedValue = cleanDomainName(originalValue);
        
        // Only update if the value changed to avoid cursor jumping
        if (originalValue !== cleanedValue) {
            const cursorPosition = this.selectionStart;
            this.value = cleanedValue;
            // Restore cursor position (accounting for removed characters)
            const removedChars = originalValue.length - cleanedValue.length;
            this.setSelectionRange(cursorPosition - removedChars, cursorPosition - removedChars);
        }
        
        duplicateCheck.innerHTML = '';
        nameInput.classList.remove('is-invalid');
    });

    // Star rating functionality
    function updateStars(rating) {
        stars.forEach((star, index) => {
            if (index < rating) {
                star.style.color = '#ffc107'; // Gold color for filled stars
            } else {
                star.style.color = '#ddd'; // Gray color for empty stars
            }
        });
    }

    // Handle star clicks
    stars.forEach((star, index) => {
        star.addEventListener('click', function() {
            const rating = index + 1;
            ratingSelect.value = rating;
            updateStars(rating);
        });

        star.addEventListener('mouseenter', function() {
            const rating = index + 1;
            updateStars(rating);
        });
    });

    // Reset stars on mouse leave
    starRating.addEventListener('mouseleave', function() {
        const currentRating = ratingSelect.value;
        updateStars(currentRating);
    });

    // Update stars when select changes
    ratingSelect.addEventListener('change', function() {
        updateStars(this.value);
    });

    // Initialize stars based on old value
    const oldRating = ratingSelect.value;
    if (oldRating) {
        updateStars(oldRating);
    }
});
</script>
@endpush