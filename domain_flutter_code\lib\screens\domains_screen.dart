import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/domain.dart';
import '../providers/domain_provider.dart';
import '../providers/category_provider.dart';
import '../utils/date_formatter.dart';
import '../utils/pagination_helper.dart';
import '../widgets/animated_card.dart';
import '../widgets/shimmer_loading.dart';
import '../widgets/star_rating.dart';
import 'add_domain_screen.dart';
import 'edit_domain_screen.dart';

class DomainsScreen extends StatefulWidget {
  const DomainsScreen({super.key});

  @override
  State<DomainsScreen> createState() => _DomainsScreenState();
}

class _DomainsScreenState extends State<DomainsScreen>
    with TickerProviderStateMixin {
  String? _selectedLetter;
  String? _searchQuery;
  int? _selectedCategoryId;
  int? _selectedRating;
  bool _showAdvancedFilters = false;
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _customExtensionController =
      TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // Extension filtering
  List<String> _selectedExtensions = [];
  List<String> _customExtensions = [];
  final List<String> _commonExtensions = ['.com', '.in'];
  final ScrollController _letterScrollController = ScrollController();

  final List<String> _letters = [
    'All',
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
  ];

  // Animation controllers
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _setupScrollListener();
    // Defer loading until after build is complete
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDomains();
      _loadCategories();
    });
  }

  Future<void> _loadCategories() async {
    final categoryProvider = Provider.of<CategoryProvider>(
      context,
      listen: false,
    );
    await categoryProvider.loadCategories();
  }

  void _setupAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOut));

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.1), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    _fadeController.forward();
    _slideController.forward();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        _loadMoreDomains();
      }
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _searchController.dispose();
    _letterScrollController.dispose();
    _customExtensionController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadDomains() async {
    final domainProvider = Provider.of<DomainProvider>(context, listen: false);

    String? searchQuery = _searchQuery;
    if (_selectedLetter != null && _selectedLetter != 'All') {
      searchQuery = _selectedLetter;
    }

    await domainProvider.loadDomains(
      refresh: true,
      search: searchQuery,
      categoryId: _selectedCategoryId,
      rating: _selectedRating,
      extensions: _selectedExtensions.isNotEmpty ? _selectedExtensions : null,
    );
  }

  Future<void> _loadMoreDomains() async {
    final domainProvider = Provider.of<DomainProvider>(context, listen: false);

    if (!domainProvider.hasMoreData || domainProvider.isLoading) return;

    // Don't load more if letter filter is active (client-side filtering)
    if (_selectedLetter != null) return;

    await domainProvider.loadDomains(
      refresh: false,
      search: _searchQuery,
      categoryId: _selectedCategoryId,
      rating: _selectedRating,
      extensions: _selectedExtensions.isNotEmpty ? _selectedExtensions : null,
    );
  }

  void _applyLetterFilter(String letter) {
    setState(() {
      _selectedLetter = letter == 'All' ? null : letter;
      _searchQuery = null;
      _searchController.clear();
    });
    _scrollToSelectedLetter(letter);
    // No need to reload domains, just update the UI
  }

  List<Domain> _getFilteredDomains(List<Domain> domains) {
    List<Domain> filteredDomains;

    if (_selectedLetter == null) {
      filteredDomains = List.from(domains);
    } else {
      filteredDomains = domains.where((domain) {
        final firstLetter = domain.name.isNotEmpty
            ? domain.name[0].toUpperCase()
            : '';
        return firstLetter == _selectedLetter;
      }).toList();
    }

    // Sort alphabetically (A-Z) by domain name
    filteredDomains.sort(
      (a, b) => a.name.toLowerCase().compareTo(b.name.toLowerCase()),
    );

    return filteredDomains;
  }

  String _capitalizeFirstLetter(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  void _scrollToSelectedLetter(String letter) {
    final index = _letters.indexOf(letter);
    if (index != -1 && _letterScrollController.hasClients) {
      final double itemWidth = 80.0; // Approximate width of each chip
      final double targetOffset = index * itemWidth;
      final double maxScroll = _letterScrollController.position.maxScrollExtent;
      final double screenWidth = MediaQuery.of(context).size.width;

      // Center the selected letter on screen
      final double centeredOffset =
          targetOffset - (screenWidth / 2) + (itemWidth / 2);
      final double clampedOffset = centeredOffset.clamp(0.0, maxScroll);

      _letterScrollController.animateTo(
        clampedOffset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _applySearchFilter(String query) {
    setState(() {
      _searchQuery = query.isNotEmpty ? query : null;
      if (query.isNotEmpty) {
        _selectedLetter = null;
      }
    });
    _loadDomains();
  }

  void _applyAdvancedFilters() {
    _loadDomains();
  }

  void _clearFilters() {
    setState(() {
      _selectedLetter = null;
      _searchQuery = null;
      _selectedCategoryId = null;
      _selectedRating = null;
      _selectedExtensions.clear();
      _searchController.clear();
    });
    _loadDomains();
  }

  void _toggleExtension(String extension) {
    setState(() {
      if (_selectedExtensions.contains(extension)) {
        _selectedExtensions.remove(extension);
      } else {
        _selectedExtensions.add(extension);
      }
    });
    _applyAdvancedFilters();
  }

  void _addCustomExtension() {
    final extension = _customExtensionController.text.trim();
    if (extension.isNotEmpty && !extension.startsWith('.')) {
      final formattedExtension = '.$extension';
      if (!_commonExtensions.contains(formattedExtension) &&
          !_customExtensions.contains(formattedExtension)) {
        setState(() {
          _customExtensions.add(formattedExtension);
          _selectedExtensions.add(formattedExtension);
          _customExtensionController.clear();
        });
        _applyAdvancedFilters();
      }
    } else if (extension.startsWith('.') && extension.length > 1) {
      if (!_commonExtensions.contains(extension) &&
          !_customExtensions.contains(extension)) {
        setState(() {
          _customExtensions.add(extension);
          _selectedExtensions.add(extension);
          _customExtensionController.clear();
        });
        _applyAdvancedFilters();
      }
    }
  }

  void _removeCustomExtension(String extension) {
    setState(() {
      _customExtensions.remove(extension);
      _selectedExtensions.remove(extension);
    });
    _applyAdvancedFilters();
  }

  bool _hasActiveFilters() {
    return _searchQuery != null ||
        _selectedCategoryId != null ||
        _selectedRating != null ||
        _selectedLetter != null ||
        _selectedExtensions.isNotEmpty;
  }

  Widget _buildActiveFiltersChips() {
    List<Widget> chips = [];

    // Letter filter chips removed - no preview needed

    if (_selectedCategoryId != null) {
      final categoryProvider = Provider.of<CategoryProvider>(
        context,
        listen: false,
      );
      try {
        final category = categoryProvider.categories.firstWhere(
          (c) => c.id == _selectedCategoryId,
        );
        chips.add(
          _buildFilterChip('Category: ${category.name}', () {
            setState(() => _selectedCategoryId = null);
            _loadDomains();
          }),
        );
      } catch (e) {
        // Category not found, clear the filter
        setState(() => _selectedCategoryId = null);
      }
    }

    if (_selectedRating != null) {
      chips.add(
        _buildRatingFilterChip(_selectedRating!, () {
          setState(() => _selectedRating = null);
          _loadDomains();
        }),
      );
    }

    for (String ext in _selectedExtensions) {
      chips.add(
        _buildFilterChip('Ext: $ext', () {
          setState(() => _selectedExtensions.remove(ext));
          _loadDomains();
        }),
      );
    }

    return Wrap(spacing: 8, runSpacing: 4, children: chips);
  }

  Widget _buildFilterChip(String label, VoidCallback onRemove) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 4),
          GestureDetector(
            onTap: onRemove,
            child: Icon(
              Icons.close_rounded,
              size: 16,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRatingFilterChip(int rating, VoidCallback onRemove) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          CompactStarRating(rating: rating, maxRating: 5, size: 12),
          const SizedBox(width: 4),
          Text(
            '+',
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 4),
          GestureDetector(
            onTap: onRemove,
            child: Icon(
              Icons.close_rounded,
              size: 16,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(domain) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Domain'),
          content: Text('Are you sure you want to delete "${domain.name}"?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _deleteDomain(domain.id);
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteDomain(int id) async {
    final domainProvider = Provider.of<DomainProvider>(context, listen: false);
    final success = await domainProvider.deleteDomain(id);

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Domain deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(domainProvider.error ?? 'Failed to delete domain'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: Column(
            children: [
              // Modern Search Bar
              Container(
                padding: const EdgeInsets.fromLTRB(20, 16, 20, 8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Theme.of(
                          context,
                        ).colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Theme.of(
                            context,
                          ).colorScheme.outline.withValues(alpha: 0.2),
                        ),
                      ),
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: 'Search domains...',
                          hintStyle: TextStyle(
                            color: Theme.of(
                              context,
                            ).colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                          prefixIcon: Icon(
                            Icons.search_rounded,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          suffixIcon: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                margin: const EdgeInsets.only(right: 8),
                                child: IconButton(
                                  icon: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: _showAdvancedFilters
                                          ? Theme.of(
                                              context,
                                            ).colorScheme.primary
                                          : Colors.transparent,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Icon(
                                      Icons.tune_rounded,
                                      size: 20,
                                      color: _showAdvancedFilters
                                          ? Colors.white
                                          : Theme.of(context)
                                                .colorScheme
                                                .onSurface
                                                .withValues(alpha: 0.6),
                                    ),
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _showAdvancedFilters =
                                          !_showAdvancedFilters;
                                    });
                                  },
                                ),
                              ),
                              if (_hasActiveFilters())
                                Container(
                                  margin: const EdgeInsets.only(right: 12),
                                  child: IconButton(
                                    icon: Container(
                                      padding: const EdgeInsets.all(6),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .error
                                            .withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      child: Icon(
                                        Icons.clear_rounded,
                                        size: 18,
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.error,
                                      ),
                                    ),
                                    onPressed: _clearFilters,
                                  ),
                                ),
                            ],
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 16,
                          ),
                        ),
                        onChanged: _applySearchFilter,
                      ),
                    ),
                    if (_hasActiveFilters()) ...[
                      const SizedBox(height: 12),
                      _buildActiveFiltersChips(),
                    ],
                  ],
                ),
              ),

              // Modern Letter Filter with Scroll
              Container(
                height: 70,
                padding: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  border: Border(
                    bottom: BorderSide(
                      color: Theme.of(
                        context,
                      ).colorScheme.outline.withValues(alpha: 0.1),
                      width: 1,
                    ),
                  ),
                ),
                child: Scrollbar(
                  controller: _letterScrollController,
                  thumbVisibility: true,
                  trackVisibility: true,
                  thickness: 4,
                  radius: const Radius.circular(2),
                  child: ListView.builder(
                    controller: _letterScrollController,
                    scrollDirection: Axis.horizontal,
                    physics: const BouncingScrollPhysics(),
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _letters.length,
                    itemBuilder: (context, index) {
                      final letter = _letters[index];
                      final isSelected = letter == 'All'
                          ? _selectedLetter == null
                          : _selectedLetter == letter;
                      return Container(
                        margin: const EdgeInsets.only(right: 8),
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          child: FilterChip(
                            label: Text(
                              letter,
                              style: TextStyle(
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.w500,
                                fontSize: 14,
                                color: isSelected
                                    ? Colors.white
                                    : Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                            selected: isSelected,
                            onSelected: (selected) =>
                                _applyLetterFilter(letter),
                            backgroundColor: Theme.of(
                              context,
                            ).colorScheme.surfaceContainerHighest,
                            selectedColor: Theme.of(
                              context,
                            ).colorScheme.primary,
                            checkmarkColor: Colors.white,
                            elevation: isSelected ? 3 : 0,
                            pressElevation: 2,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),

              // Advanced Filters
              if (_showAdvancedFilters)
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Consumer<CategoryProvider>(
                    builder: (context, categoryProvider, child) {
                      return Column(
                        children: [
                          // Category Filter
                          DropdownButtonFormField<int>(
                            value: _selectedCategoryId,
                            decoration: const InputDecoration(
                              labelText: 'Filter by Category',
                              border: OutlineInputBorder(),
                            ),
                            items: [
                              const DropdownMenuItem<int>(
                                value: null,
                                child: Text('All Categories'),
                              ),
                              ...categoryProvider.categories.map((category) {
                                return DropdownMenuItem<int>(
                                  value: category.id,
                                  child: Text(
                                    '${category.name} (${category.buyDomainsCount})',
                                  ),
                                );
                              }),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _selectedCategoryId = value;
                              });
                              _applyAdvancedFilters();
                            },
                          ),
                          const SizedBox(height: 16),

                          // Rating Filter
                          DropdownButtonFormField<int>(
                            value: _selectedRating,
                            decoration: const InputDecoration(
                              labelText: 'Filter by Rating',
                              border: OutlineInputBorder(),
                            ),
                            items: [
                              const DropdownMenuItem<int>(
                                value: null,
                                child: Text('All Ratings'),
                              ),
                              ...List.generate(5, (index) {
                                final rating = index + 1;
                                return DropdownMenuItem<int>(
                                  value: rating,
                                  child: Row(
                                    children: [
                                      CompactStarRating(
                                        rating: rating,
                                        maxRating: 5,
                                        size: 16,
                                      ),
                                      const SizedBox(width: 8),
                                      Text('& Above'),
                                    ],
                                  ),
                                );
                              }),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _selectedRating = value;
                              });
                              _applyAdvancedFilters();
                            },
                          ),
                          const SizedBox(height: 16),

                          // Extension Filter
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Filter by Extensions',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),

                              // Common Extensions
                              Wrap(
                                spacing: 8,
                                runSpacing: 4,
                                children: _commonExtensions.map((extension) {
                                  final isSelected = _selectedExtensions
                                      .contains(extension);
                                  return FilterChip(
                                    label: Text(extension),
                                    selected: isSelected,
                                    onSelected: (selected) =>
                                        _toggleExtension(extension),
                                    backgroundColor: Colors.grey.shade200,
                                    selectedColor: Theme.of(
                                      context,
                                    ).primaryColor.withValues(alpha: 0.3),
                                  );
                                }).toList(),
                              ),

                              // Custom Extensions
                              if (_customExtensions.isNotEmpty) ...[
                                const SizedBox(height: 8),
                                Wrap(
                                  spacing: 8,
                                  runSpacing: 4,
                                  children: _customExtensions.map((extension) {
                                    final isSelected = _selectedExtensions
                                        .contains(extension);
                                    return FilterChip(
                                      label: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(extension),
                                          const SizedBox(width: 4),
                                          GestureDetector(
                                            onTap: () => _removeCustomExtension(
                                              extension,
                                            ),
                                            child: const Icon(
                                              Icons.close,
                                              size: 16,
                                              color: Colors.red,
                                            ),
                                          ),
                                        ],
                                      ),
                                      selected: isSelected,
                                      onSelected: (selected) =>
                                          _toggleExtension(extension),
                                      backgroundColor: Colors.orange.shade100,
                                      selectedColor: Colors.orange.shade300,
                                    );
                                  }).toList(),
                                ),
                              ],

                              const SizedBox(height: 8),

                              // Add Custom Extension
                              Row(
                                children: [
                                  Expanded(
                                    child: TextField(
                                      controller: _customExtensionController,
                                      decoration: const InputDecoration(
                                        hintText:
                                            'Add custom extension (e.g., .xyz)',
                                        border: OutlineInputBorder(),
                                        isDense: true,
                                      ),
                                      onSubmitted: (_) => _addCustomExtension(),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  ElevatedButton(
                                    onPressed: _addCustomExtension,
                                    child: const Text('Add'),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      );
                    },
                  ),
                ),

              // Modern Domains List
              Expanded(
                child: Consumer<DomainProvider>(
                  builder: (context, domainProvider, child) {
                    if (domainProvider.isLoading &&
                        domainProvider.domains.isEmpty) {
                      return _buildLoadingState();
                    }

                    if (domainProvider.error != null &&
                        domainProvider.domains.isEmpty) {
                      return PaginationHelper.buildErrorState(
                        title: 'Error loading domains',
                        message: domainProvider.error!,
                        onRetry: _loadDomains,
                      );
                    }

                    if (domainProvider.domains.isEmpty) {
                      return PaginationHelper.buildEmptyState(
                        icon: Icons.domain_rounded,
                        title: 'No domains found',
                        subtitle:
                            'Try adjusting your search filters or add a new domain',
                        action: ElevatedButton.icon(
                          onPressed: () async {
                            await Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const AddDomainScreen(),
                              ),
                            );
                            _loadDomains();
                          },
                          icon: const Icon(Icons.add),
                          label: const Text('Add Domain'),
                        ),
                      );
                    }

                    final filteredDomains = _getFilteredDomains(
                      domainProvider.domains,
                    );

                    return RefreshIndicator(
                      onRefresh: _loadDomains,
                      color: Theme.of(context).colorScheme.primary,
                      child: ListView.builder(
                        controller: _scrollController,
                        physics: const AlwaysScrollableScrollPhysics(),
                        padding: const EdgeInsets.all(20),
                        itemCount: filteredDomains.length + 1,
                        itemBuilder: (context, index) {
                          if (index == filteredDomains.length) {
                            // Only show pagination if no letter filter is active
                            if (_selectedLetter == null) {
                              return PaginationHelper.buildPaginationIndicator(
                                isLoading: domainProvider.isLoading,
                                hasMoreData: domainProvider.hasMoreData,
                                onLoadMore: _loadMoreDomains,
                                color: Theme.of(context).colorScheme.primary,
                              );
                            } else {
                              return const SizedBox.shrink();
                            }
                          }

                          final domain = filteredDomains[index];
                          return _buildDomainCard(domain, index);
                        },
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const AddDomainScreen()),
          );
          _loadDomains();
        },
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add_rounded),
      ),
    );
  }

  Widget _buildLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: 6,
      itemBuilder: (context, index) {
        return ShimmerLoading(
          isLoading: true,
          child: Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Container(
              height: 120,
              padding: const EdgeInsets.all(20),
              child: const ShimmerListTile(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDomainCard(domain, int index) {
    return AnimatedCard(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      onTap: () async {
        await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => EditDomainScreen(domain: domain),
          ),
        );
        _loadDomains();
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.primary,
                      Theme.of(
                        context,
                      ).colorScheme.primary.withValues(alpha: 0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Center(
                  child: Text(
                    domain.name.substring(0, 1).toUpperCase(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _capitalizeFirstLetter(domain.name),
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    _buildExtensionsDisplay(domain.extensions),
                  ],
                ),
              ),
              _buildAvailabilityIndicator(domain.isAvailable),
            ],
          ),
          const SizedBox(height: 16),
          // Categories Section
          Consumer<CategoryProvider>(
            builder: (context, categoryProvider, child) {
              final categories = categoryProvider.getCategoriesByIds(
                domain.categories,
              );
              if (categories.isNotEmpty) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Categories',
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 4,
                      children: categories.map((category) {
                        Color categoryColor;
                        try {
                          categoryColor = Color(
                            int.parse(category.color.substring(1), radix: 16) +
                                0xFF000000,
                          );
                        } catch (e) {
                          categoryColor = Theme.of(context).colorScheme.primary;
                        }

                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: categoryColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: categoryColor.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                width: 8,
                                height: 8,
                                decoration: BoxDecoration(
                                  color: categoryColor,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 6),
                              Text(
                                category.name,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: categoryColor.withValues(alpha: 0.8),
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: 16),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    Icons.calendar_today_rounded,
                    'Expiry',
                    DateFormatter.formatDate(domain.expiryDate),
                  ),
                ),
                Container(
                  width: 1,
                  height: 30,
                  color: Theme.of(
                    context,
                  ).colorScheme.outline.withValues(alpha: 0.2),
                ),
                Expanded(
                  child: _buildRatingInfoItem('Rating', domain.rating ?? 0),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              OutlinedButton.icon(
                onPressed: () async {
                  await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => EditDomainScreen(domain: domain),
                    ),
                  );
                  _loadDomains();
                },
                icon: const Icon(Icons.edit_rounded, size: 18),
                label: const Text('Edit'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              OutlinedButton.icon(
                onPressed: () => _showDeleteDialog(domain),
                icon: const Icon(Icons.delete_rounded, size: 18),
                label: const Text('Delete'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.error,
                  side: BorderSide(color: Theme.of(context).colorScheme.error),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAvailabilityIndicator(bool? isAvailable) {
    if (isAvailable == null) {
      return Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(
          Icons.help_outline_rounded,
          color: Colors.grey[600],
          size: 20,
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: isAvailable
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        isAvailable ? Icons.check_circle_rounded : Icons.cancel_rounded,
        color: isAvailable ? Colors.green : Colors.red,
        size: 20,
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Theme.of(context).colorScheme.primary),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
              Text(
                value,
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRatingInfoItem(String label, int rating) {
    return Row(
      children: [
        Icon(
          Icons.star_rounded,
          size: 16,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
              const SizedBox(height: 2),
              CompactStarRating(rating: rating, maxRating: 5, size: 14),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildExtensionsDisplay(List<String> domainExtensions) {
    // Standard extensions that are always shown
    final List<String> standardExtensions = ['.com', '.in'];

    // Normalize domain extensions (ensure they start with .)
    final List<String> normalizedExtensions = domainExtensions.map((ext) {
      String extension = ext.toString();
      if (!extension.startsWith('.')) {
        extension = '.$extension';
      }
      return extension;
    }).toList();

    // Collect custom extensions (not .com or .in)
    final List<String> customExtensions = [];
    for (String ext in normalizedExtensions) {
      if (!standardExtensions.contains(ext)) {
        customExtensions.add(ext);
      }
    }

    return Wrap(
      spacing: 6,
      runSpacing: 4,
      children: [
        // Show standard extensions (.com, .in) with availability status
        ...standardExtensions.map((ext) {
          final bool isSelected = normalizedExtensions.contains(ext);
          return _buildExtensionBadge(ext, isSelected, false);
        }),

        // Show custom extensions (always available/green)
        ...customExtensions.map((ext) {
          return _buildExtensionBadge(ext, true, true);
        }),
      ],
    );
  }

  Widget _buildExtensionBadge(
    String extension,
    bool isAvailable,
    bool isCustom,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
        vertical: 6,
      ), // Increased padding
      decoration: BoxDecoration(
        color: isAvailable
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        border: Border.all(
          color: isAvailable
              ? Colors.green.withValues(alpha: 0.3)
              : Colors.red.withValues(alpha: 0.3),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(8), // Slightly larger border radius
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isAvailable ? Icons.check : Icons.close,
            size: 16, // Increased from 12 to 16
            color: isAvailable ? Colors.green[700] : Colors.red[700],
          ),
          const SizedBox(width: 6), // Increased spacing
          Text(
            extension,
            style: TextStyle(
              fontSize: 14, // Increased from 11 to 14
              fontWeight:
                  FontWeight.w600, // Increased weight for better visibility
              color: isAvailable ? Colors.green[700] : Colors.red[700],
            ),
          ),
        ],
      ),
    );
  }
}
