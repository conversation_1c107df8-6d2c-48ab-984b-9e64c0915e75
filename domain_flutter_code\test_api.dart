import 'dart:convert';
import 'package:http/http.dart' as http;

void main() async {
  print('Testing API connectivity...');

  const baseUrl = 'https://domain.agfgroupindia.com';

  // Test multiple possible login endpoints
  final loginEndpoints = [
    '/admin/login', // Website admin login
    '/api/auth/login', // Standard API login
    '/api/admin/login', // Alternative API admin login
    '/public/api/auth/login', // Laravel public API
  ];

  try {
    // Test 1: Basic server connectivity
    print('\n1. Testing basic server connectivity...');
    final response1 = await http
        .get(Uri.parse(baseUrl))
        .timeout(const Duration(seconds: 10));
    print('✅ Server reachable: ${response1.statusCode}');

    // Test 2: Try each login endpoint
    print('\n2. Testing login endpoints...');

    for (int i = 0; i < loginEndpoints.length; i++) {
      final endpoint = loginEndpoints[i];
      print('\n2.${i + 1}. Testing endpoint: $endpoint');

      try {
        final response = await http
            .post(
              Uri.parse('$baseUrl$endpoint'),
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
              },
              body: json.encode({
                'email': '<EMAIL>',
                'password': 'testpassword',
              }),
            )
            .timeout(const Duration(seconds: 10));

        print('   Status: ${response.statusCode}');
        print('   Headers: ${response.headers}');
        print('   Body: ${response.body}');

        // Check if response is JSON
        if (response.body.isNotEmpty) {
          try {
            final jsonResponse = json.decode(response.body);
            print('   ✅ Valid JSON response');
            print('   Response keys: ${jsonResponse.keys}');
          } catch (e) {
            print('   ⚠️ Non-JSON response');
          }
        }

        // If we get a response (even error), the endpoint exists
        if (response.statusCode >= 200 && response.statusCode < 500) {
          print('   ✅ Endpoint is functional');
        }
      } catch (e) {
        print('   ❌ Endpoint failed: $e');
      }
    }

    print('\n🎉 API connectivity test completed!');
  } catch (e) {
    print('❌ API connectivity test failed: $e');

    if (e.toString().contains('SocketException')) {
      print('💡 This appears to be a network connectivity issue.');
    } else if (e.toString().contains('TimeoutException')) {
      print('💡 The request timed out. Server might be slow or unreachable.');
    } else {
      print('💡 Unexpected error occurred.');
    }
  }
}
