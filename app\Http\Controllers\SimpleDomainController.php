<?php

namespace App\Http\Controllers;

use App\Models\SimpleDomain;
use App\Models\Category;
use App\Rules\DomainNameRule;
use Illuminate\Http\Request;

class SimpleDomainController extends Controller
{
    public function index(Request $request)
    {
        $query = SimpleDomain::query();

        // Apply filters
        if ($request->filled('domain_name')) {
            $query->where('name', 'like', '%' . $request->domain_name . '%');
        }

        if ($request->filled('category_id')) {
            $query->byCategory($request->category_id);
        }

        // Apply alphabetical filter
        if ($request->filled('letter')) {
            $letter = strtolower($request->letter);
            $query->where('name', 'like', $letter . '%');
        }

        $simpleDomains = $query->orderBy('created_at', 'desc')->paginate(10);
        $categories = Category::all();

        if ($request->ajax()) {
            return response()->json([
                'domains' => $simpleDomains->items(),
                'pagination' => [
                    'current_page' => $simpleDomains->currentPage(),
                    'last_page' => $simpleDomains->lastPage(),
                    'total' => $simpleDomains->total()
                ]
            ]);
        }

        return view('simple-domains.index', compact('simpleDomains', 'categories'));
    }

    public function create()
    {
        $categories = Category::all();
        return view('simple-domains.create', compact('categories'));
    }

    public function store(Request $request)
    {
        // Clean domain name before validation
        $request->merge([
            'name' => $this->cleanDomainName($request->name)
        ]);

        $request->validate([
            'name' => ['required', 'string', new DomainNameRule, 'unique:simple_domains,name'],
            'categories' => 'required|array|min:1',
        ]);

        // Create simple domain with only name and categories
        $simpleDomain = SimpleDomain::create([
            'name' => $request->name,
            'categories' => $request->categories,
        ]);

        if ($request->ajax()) {
            return response()->json(['success' => true, 'domain' => $simpleDomain]);
        }

        return redirect()->route('simple-domains.index')->with('success', 'Reserve Domain created successfully!');
    }

    public function show(SimpleDomain $simpleDomain)
    {
        return view('simple-domains.show', compact('simpleDomain'));
    }

    public function edit(SimpleDomain $simpleDomain)
    {
        $categories = Category::all();
        return view('simple-domains.edit', compact('simpleDomain', 'categories'));
    }

    public function update(Request $request, SimpleDomain $simpleDomain)
    {
        // Clean domain name before validation
        $request->merge([
            'name' => $this->cleanDomainName($request->name)
        ]);

        $request->validate([
            'name' => ['required', 'string', new DomainNameRule, 'unique:simple_domains,name,' . $simpleDomain->id],
            'categories' => 'required|array|min:1',
        ]);

        $simpleDomain->update($request->all());

        if ($request->ajax()) {
            return response()->json(['success' => true, 'domain' => $simpleDomain]);
        }

        return redirect()->route('simple-domains.index')->with('success', 'Reserve Domain updated successfully!');
    }

    public function destroy(SimpleDomain $simpleDomain)
    {
        $simpleDomain->delete();

        if (request()->ajax()) {
            return response()->json(['success' => true]);
        }

        return redirect()->route('simple-domains.index')->with('success', 'Reserve Domain deleted successfully!');
    }

    public function checkDuplicate(Request $request)
    {
        $query = SimpleDomain::where('name', $request->name);
        
        // If ignore_id is provided (for edit functionality), exclude that record
        if ($request->has('ignore_id')) {
            $query->where('id', '!=', $request->ignore_id);
        }
        
        $exists = $query->exists();
        return response()->json(['exists' => $exists]);
    }

    public function buy(SimpleDomain $simpleDomain)
    {
        $categories = Category::all();
        return view('simple-domains.buy', compact('simpleDomain', 'categories'));
    }

    /**
     * Clean domain name by removing spaces and converting to lowercase
     */
    private function cleanDomainName($domainName)
    {
        if (!$domainName) {
            return $domainName;
        }
        
        // Remove all spaces and convert to lowercase
        return strtolower(str_replace(' ', '', trim($domainName)));
    }
}