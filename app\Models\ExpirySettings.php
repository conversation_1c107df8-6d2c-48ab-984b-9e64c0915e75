<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ExpirySettings extends Model
{
    protected $fillable = [
        'admin_email',
        'alert_days',
        'is_active',
        'smtp_host',
        'smtp_port',
        'smtp_username',
        'smtp_password',
        'smtp_encryption',
        'mail_from_address',
        'mail_from_name'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'smtp_port' => 'integer'
    ];

    // Get alert days as array
    public function getAlertDaysArrayAttribute()
    {
        if (empty($this->alert_days)) {
            return [30, 15, 7, 1]; // Default values
        }
        
        return array_map('intval', explode(',', str_replace(' ', '', $this->alert_days)));
    }

    // Get or create settings instance
    public static function getSettings()
    {
        $settings = self::first();
        
        if (!$settings) {
            $settings = self::create([
                'admin_email' => config('mail.from.address'),
                'alert_days' => '30,15,7,1',
                'is_active' => true,
                'smtp_host' => config('mail.mailers.smtp.host'),
                'smtp_port' => config('mail.mailers.smtp.port'),
                'smtp_username' => config('mail.mailers.smtp.username'),
                'smtp_password' => config('mail.mailers.smtp.password'),
                'smtp_encryption' => 'tls',
                'mail_from_address' => config('mail.from.address'),
                'mail_from_name' => config('mail.from.name')
            ]);
        }
        
        return $settings;
    }

    // Update mail configuration dynamically
    public function updateMailConfig()
    {
        if ($this->smtp_host && $this->smtp_username && $this->smtp_password) {
            config([
                'mail.default' => 'smtp',
                'mail.mailers.smtp.host' => $this->smtp_host,
                'mail.mailers.smtp.port' => $this->smtp_port ?: 587,
                'mail.mailers.smtp.username' => $this->smtp_username,
                'mail.mailers.smtp.password' => $this->smtp_password,
                'mail.mailers.smtp.encryption' => $this->smtp_encryption ?: 'tls',
                'mail.from.address' => $this->mail_from_address ?: $this->admin_email,
                'mail.from.name' => $this->mail_from_name ?: config('app.name')
            ]);
        }
    }
}
