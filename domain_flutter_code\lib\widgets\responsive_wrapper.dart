import 'package:flutter/material.dart';
import '../utils/theme.dart';

/// A responsive wrapper that prevents overflow issues
/// by adjusting padding and spacing based on screen size
class ResponsiveWrapper extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final bool preventOverflow;
  final double? maxWidth;

  const ResponsiveWrapper({
    super.key,
    required this.child,
    this.padding,
    this.preventOverflow = true,
    this.maxWidth,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenHeight = constraints.maxHeight;
        final screenWidth = constraints.maxWidth;
        
        // Determine if it's a small screen
        final isSmallScreen = screenHeight < 600 || screenWidth < 400;
        final isVerySmallScreen = screenHeight < 500 || screenWidth < 350;
        
        // Calculate responsive padding
        EdgeInsets responsivePadding;
        if (padding != null) {
          responsivePadding = padding!;
        } else if (isVerySmallScreen) {
          responsivePadding = const EdgeInsets.all(AppTheme.spaceSM);
        } else if (isSmallScreen) {
          responsivePadding = const EdgeInsets.all(AppTheme.spaceMD);
        } else {
          responsivePadding = const EdgeInsets.all(AppTheme.spaceLG);
        }

        Widget wrappedChild = child;

        // Apply max width constraint if specified
        if (maxWidth != null) {
          wrappedChild = ConstrainedBox(
            constraints: BoxConstraints(maxWidth: maxWidth!),
            child: wrappedChild,
          );
        }

        // Wrap with padding
        wrappedChild = Padding(
          padding: responsivePadding,
          child: wrappedChild,
        );

        // Add overflow prevention if enabled
        if (preventOverflow) {
          wrappedChild = SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: screenHeight - responsivePadding.vertical,
              ),
              child: IntrinsicHeight(
                child: wrappedChild,
              ),
            ),
          );
        }

        return wrappedChild;
      },
    );
  }
}

/// A responsive column that adjusts spacing based on screen size
class ResponsiveColumn extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;

  const ResponsiveColumn({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxHeight < 600;
        
        // Add responsive spacing between children
        final spacedChildren = <Widget>[];
        for (int i = 0; i < children.length; i++) {
          spacedChildren.add(children[i]);
          if (i < children.length - 1) {
            spacedChildren.add(SizedBox(
              height: isSmallScreen ? AppTheme.spaceSM : AppTheme.spaceMD,
            ));
          }
        }

        return Column(
          mainAxisAlignment: mainAxisAlignment,
          crossAxisAlignment: crossAxisAlignment,
          mainAxisSize: mainAxisSize,
          children: spacedChildren,
        );
      },
    );
  }
}

/// A responsive row that adjusts spacing based on screen size
class ResponsiveRow extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;

  const ResponsiveRow({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < 400;
        
        // Add responsive spacing between children
        final spacedChildren = <Widget>[];
        for (int i = 0; i < children.length; i++) {
          spacedChildren.add(children[i]);
          if (i < children.length - 1) {
            spacedChildren.add(SizedBox(
              width: isSmallScreen ? AppTheme.spaceSM : AppTheme.spaceMD,
            ));
          }
        }

        return Row(
          mainAxisAlignment: mainAxisAlignment,
          crossAxisAlignment: crossAxisAlignment,
          mainAxisSize: mainAxisSize,
          children: spacedChildren,
        );
      },
    );
  }
}

/// Utility class for responsive values
class ResponsiveUtils {
  static double getResponsiveValue(
    BuildContext context, {
    required double small,
    required double medium,
    required double large,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 400) {
      return small;
    } else if (screenWidth < 600) {
      return medium;
    } else {
      return large;
    }
  }

  static EdgeInsets getResponsivePadding(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenHeight < 500 || screenWidth < 350) {
      return const EdgeInsets.all(AppTheme.spaceSM);
    } else if (screenHeight < 600 || screenWidth < 400) {
      return const EdgeInsets.all(AppTheme.spaceMD);
    } else {
      return const EdgeInsets.all(AppTheme.spaceLG);
    }
  }

  static bool isSmallScreen(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return size.height < 600 || size.width < 400;
  }

  static bool isVerySmallScreen(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return size.height < 500 || size.width < 350;
  }
}
