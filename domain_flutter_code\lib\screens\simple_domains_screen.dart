import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/simple_domain.dart';
import '../models/category.dart';
import '../providers/domain_provider.dart';
import '../providers/category_provider.dart';
import '../utils/date_formatter.dart';

import 'add_simple_domain_screen.dart';
import 'edit_simple_domain_screen.dart';
import '../widgets/buy_domain_dialog.dart';

class SimpleDomainsScreen extends StatefulWidget {
  const SimpleDomainsScreen({super.key});

  @override
  State<SimpleDomainsScreen> createState() => _SimpleDomainsScreenState();
}

class _SimpleDomainsScreenState extends State<SimpleDomainsScreen>
    with TickerProviderStateMixin, RouteAware {
  String? _selectedLetter;
  String? _searchQuery;
  int? _selectedCategoryId;
  bool _showAdvancedFilters = false;
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _letterScrollController = ScrollController();
  final ScrollController _scrollController = ScrollController();

  final List<String> _letters = [
    'All',
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
  ];

  // Animation controllers
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _setupScrollListener();
    // Defer loading until after build is complete
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSimpleDomains();
      _loadCategories();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Auto-refresh when navigating to this page
    if (ModalRoute.of(context)?.isCurrent == true) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loadSimpleDomains();
      });
    }
  }

  // RouteAware methods for automatic refresh when returning to page
  @override
  void didPopNext() {
    // Called when returning to this page from another page
    _loadSimpleDomains();
  }

  @override
  void didPushNext() {
    // Called when navigating away from this page
  }

  Future<void> _loadCategories() async {
    final categoryProvider = Provider.of<CategoryProvider>(
      context,
      listen: false,
    );
    await categoryProvider.loadCategories();
  }

  void _setupAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOut));

    _fadeController.forward();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        _loadMoreSimpleDomains();
      }
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _searchController.dispose();
    _letterScrollController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadSimpleDomains() async {
    final domainProvider = Provider.of<DomainProvider>(context, listen: false);

    String? searchQuery = _searchQuery;
    if (_selectedLetter != null) {
      searchQuery = _selectedLetter;
    }

    await domainProvider.loadSimpleDomains(
      refresh: true,
      search: searchQuery,
      categoryId: _selectedCategoryId,
    );
  }

  Future<void> _loadMoreSimpleDomains() async {
    final domainProvider = Provider.of<DomainProvider>(context, listen: false);

    if (!domainProvider.hasMoreData || domainProvider.isLoading) return;

    String? searchQuery = _searchQuery;
    if (_selectedLetter != null) {
      searchQuery = _selectedLetter;
    }

    await domainProvider.loadSimpleDomains(
      refresh: false,
      search: searchQuery,
      categoryId: _selectedCategoryId,
    );
  }

  void _applyLetterFilter(String letter) {
    setState(() {
      _selectedLetter = letter == 'All' ? null : letter;
      _searchQuery = null;
      _searchController.clear();
    });
    _scrollToSelectedLetter(letter);
    // No need to reload domains, just update the UI
  }

  List<SimpleDomain> _getFilteredSimpleDomains(List<SimpleDomain> domains) {
    List<SimpleDomain> filteredDomains;

    if (_selectedLetter == null) {
      filteredDomains = List.from(domains);
    } else {
      filteredDomains = domains.where((domain) {
        final firstLetter = domain.name.isNotEmpty
            ? domain.name[0].toUpperCase()
            : '';
        return firstLetter == _selectedLetter;
      }).toList();
    }

    // Sort alphabetically (A-Z) by domain name
    filteredDomains.sort(
      (a, b) => a.name.toLowerCase().compareTo(b.name.toLowerCase()),
    );

    return filteredDomains;
  }

  String _capitalizeFirstLetter(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  void _scrollToSelectedLetter(String letter) {
    final index = _letters.indexOf(letter);
    if (index != -1 && _letterScrollController.hasClients) {
      final double itemWidth = 80.0; // Approximate width of each chip
      final double targetOffset = index * itemWidth;
      final double maxScroll = _letterScrollController.position.maxScrollExtent;
      final double screenWidth = MediaQuery.of(context).size.width;

      // Center the selected letter on screen
      final double centeredOffset =
          targetOffset - (screenWidth / 2) + (itemWidth / 2);
      final double clampedOffset = centeredOffset.clamp(0.0, maxScroll);

      _letterScrollController.animateTo(
        clampedOffset,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _applySearchFilter(String query) {
    setState(() {
      _searchQuery = query.isNotEmpty ? query : null;
      if (query.isNotEmpty) {
        _selectedLetter = null;
      }
    });
    _loadSimpleDomains();
  }

  void _applyAdvancedFilters() {
    _loadSimpleDomains();
  }

  void _clearFilters() {
    setState(() {
      _selectedLetter = null;
      _searchQuery = null;
      _selectedCategoryId = null;
      _searchController.clear();
    });
    _loadSimpleDomains();
  }

  void _showDeleteDialog(domain) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Reserved Domain'),
          content: Text('Are you sure you want to delete "${domain.name}"?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _deleteSimpleDomain(domain.id);
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteSimpleDomain(int id) async {
    final domainProvider = Provider.of<DomainProvider>(context, listen: false);
    final success = await domainProvider.deleteSimpleDomain(id);

    if (mounted) {
      if (success) {
        // Force refresh the list after successful deletion
        await _loadSimpleDomains();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Reserved domain deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              domainProvider.error ?? 'Failed to delete reserved domain',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showBuyDialog(SimpleDomain domain) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return BuyDomainDialog(domain: domain);
      },
    ).then((_) {
      // Refresh the list when returning from buy dialog
      _loadSimpleDomains();
    });
  }

  bool _hasActiveFilters() {
    return _searchQuery != null ||
        _selectedCategoryId != null ||
        _selectedLetter != null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Modern Search Bar
            Container(
              padding: const EdgeInsets.fromLTRB(20, 16, 20, 8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Theme.of(
                      context,
                    ).colorScheme.outline.withValues(alpha: 0.2),
                  ),
                ),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search reserved domains...',
                    hintStyle: TextStyle(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    prefixIcon: Icon(
                      Icons.search_rounded,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    suffixIcon: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          margin: const EdgeInsets.only(right: 8),
                          child: IconButton(
                            icon: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: _showAdvancedFilters
                                    ? Theme.of(context).colorScheme.primary
                                    : Colors.transparent,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                Icons.tune_rounded,
                                size: 20,
                                color: _showAdvancedFilters
                                    ? Colors.white
                                    : Theme.of(context).colorScheme.onSurface
                                          .withValues(alpha: 0.6),
                              ),
                            ),
                            onPressed: () {
                              setState(() {
                                _showAdvancedFilters = !_showAdvancedFilters;
                              });
                            },
                          ),
                        ),
                        if (_hasActiveFilters())
                          Container(
                            margin: const EdgeInsets.only(right: 12),
                            child: IconButton(
                              icon: Container(
                                padding: const EdgeInsets.all(6),
                                decoration: BoxDecoration(
                                  color: Theme.of(
                                    context,
                                  ).colorScheme.error.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Icon(
                                  Icons.clear_rounded,
                                  size: 18,
                                  color: Theme.of(context).colorScheme.error,
                                ),
                              ),
                              onPressed: _clearFilters,
                            ),
                          ),
                      ],
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 16,
                    ),
                  ),
                  onChanged: _applySearchFilter,
                ),
              ),
            ),

            // Modern Letter Filter with Scroll (Same as Domain Page)
            Container(
              height: 70,
              padding: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                border: Border(
                  bottom: BorderSide(
                    color: Theme.of(
                      context,
                    ).colorScheme.outline.withValues(alpha: 0.1),
                    width: 1,
                  ),
                ),
              ),
              child: Scrollbar(
                controller: _letterScrollController,
                thumbVisibility: true,
                trackVisibility: true,
                thickness: 4,
                radius: const Radius.circular(2),
                child: ListView.builder(
                  controller: _letterScrollController,
                  scrollDirection: Axis.horizontal,
                  physics: const BouncingScrollPhysics(),
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _letters.length,
                  itemBuilder: (context, index) {
                    final letter = _letters[index];
                    final isSelected = letter == 'All'
                        ? _selectedLetter == null
                        : _selectedLetter == letter;
                    return Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        child: FilterChip(
                          label: Text(
                            letter,
                            style: TextStyle(
                              fontWeight: isSelected
                                  ? FontWeight.bold
                                  : FontWeight.w500,
                              fontSize: 14,
                              color: isSelected
                                  ? Colors.white
                                  : Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          selected: isSelected,
                          onSelected: (selected) => _applyLetterFilter(letter),
                          backgroundColor: Theme.of(
                            context,
                          ).colorScheme.surfaceContainerHighest,
                          selectedColor: Theme.of(context).colorScheme.primary,
                          checkmarkColor: Colors.white,
                          elevation: isSelected ? 3 : 0,
                          pressElevation: 2,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),

            // Advanced Filters
            if (_showAdvancedFilters)
              Container(
                padding: const EdgeInsets.all(16),
                child: Consumer<CategoryProvider>(
                  builder: (context, categoryProvider, child) {
                    return DropdownButtonFormField<int>(
                      value: _selectedCategoryId,
                      decoration: const InputDecoration(
                        labelText: 'Filter by Category',
                        border: OutlineInputBorder(),
                      ),
                      items: [
                        const DropdownMenuItem<int>(
                          value: null,
                          child: Text('All Categories'),
                        ),
                        ...categoryProvider.categories.map((category) {
                          return DropdownMenuItem<int>(
                            value: category.id,
                            child: Text(
                              '${category.name} (${category.reserveDomainsCount})',
                            ),
                          );
                        }),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedCategoryId = value;
                        });
                        _applyAdvancedFilters();
                      },
                    );
                  },
                ),
              ),

            // Simple Domains List
            Expanded(
              child: Consumer<DomainProvider>(
                builder: (context, domainProvider, child) {
                  // Show loading only when there's no data and we're loading
                  if (domainProvider.isLoading &&
                      domainProvider.simpleDomains.isEmpty) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('Loading reserved domains...'),
                        ],
                      ),
                    );
                  }

                  // Show error only when there's no data and there's an error
                  if (domainProvider.error != null &&
                      domainProvider.simpleDomains.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 64,
                            color: Colors.red[300],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error loading reserved domains',
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            domainProvider.error!,
                            textAlign: TextAlign.center,
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _loadSimpleDomains,
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  }

                  // Show empty state only when not loading and no error
                  if (domainProvider.simpleDomains.isEmpty &&
                      !domainProvider.isLoading &&
                      domainProvider.error == null) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.bookmark,
                            size: 64,
                            color: Colors.grey,
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'No reserved domains found',
                            style: TextStyle(fontSize: 18, color: Colors.grey),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _loadSimpleDomains,
                            child: const Text('Refresh'),
                          ),
                        ],
                      ),
                    );
                  }

                  final filteredDomains = _getFilteredSimpleDomains(
                    domainProvider.simpleDomains,
                  );

                  return RefreshIndicator(
                    onRefresh: _loadSimpleDomains,
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: filteredDomains.length,
                      itemBuilder: (context, index) {
                        final domain = filteredDomains[index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: 12),
                          child: InkWell(
                            onTap: () async {
                              await Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      EditSimpleDomainScreen(domain: domain),
                                ),
                              );
                              // Refresh the list when returning from edit screen
                              _loadSimpleDomains();
                            },
                            borderRadius: BorderRadius.circular(12),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      CircleAvatar(
                                        backgroundColor: Colors.green,
                                        child: Text(
                                          domain.name
                                              .substring(0, 1)
                                              .toUpperCase(),
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              _capitalizeFirstLetter(
                                                domain.name,
                                              ),
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .titleMedium
                                                  ?.copyWith(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'Created: ${DateFormatter.formatDate(domain.createdAt)}',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodySmall
                                                  ?.copyWith(
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .onSurface
                                                        .withValues(alpha: 0.6),
                                                  ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          IconButton(
                                            icon: const Icon(Icons.edit),
                                            onPressed: () async {
                                              await Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      EditSimpleDomainScreen(
                                                        domain: domain,
                                                      ),
                                                ),
                                              );
                                              // Refresh the list when returning from edit screen
                                              _loadSimpleDomains();
                                            },
                                          ),
                                          IconButton(
                                            icon: const Icon(
                                              Icons.delete,
                                              color: Colors.red,
                                            ),
                                            onPressed: () =>
                                                _showDeleteDialog(domain),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  // Categories Section with Buy Button
                                  Consumer<CategoryProvider>(
                                    builder: (context, categoryProvider, child) {
                                      final categories = categoryProvider
                                          .getCategoriesByIds(
                                            domain.categories,
                                          );
                                      if (categories.isNotEmpty) {
                                        return Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text(
                                                  'Categories',
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .labelSmall
                                                      ?.copyWith(
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        color: Theme.of(context)
                                                            .colorScheme
                                                            .onSurface
                                                            .withValues(
                                                              alpha: 0.7,
                                                            ),
                                                      ),
                                                ),
                                                // Compact Buy Button
                                                Container(
                                                  width: 32,
                                                  height: 32,
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          8,
                                                        ),
                                                    gradient:
                                                        const LinearGradient(
                                                          colors: [
                                                            Color(0xFF4CAF50),
                                                            Color(0xFF45A049),
                                                          ],
                                                          begin:
                                                              Alignment.topLeft,
                                                          end: Alignment
                                                              .bottomRight,
                                                        ),
                                                    boxShadow: [
                                                      BoxShadow(
                                                        color:
                                                            const Color(
                                                              0xFF4CAF50,
                                                            ).withValues(
                                                              alpha: 0.2,
                                                            ),
                                                        blurRadius: 4,
                                                        offset: const Offset(
                                                          0,
                                                          2,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  child: Material(
                                                    color: Colors.transparent,
                                                    child: InkWell(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            8,
                                                          ),
                                                      onTap: () =>
                                                          _showBuyDialog(
                                                            domain,
                                                          ),
                                                      child: const Center(
                                                        child: Icon(
                                                          Icons
                                                              .shopping_cart_rounded,
                                                          size: 14,
                                                          color: Colors.white,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            const SizedBox(height: 6),
                                            Wrap(
                                              spacing: 6,
                                              runSpacing: 4,
                                              children: categories.map((
                                                category,
                                              ) {
                                                Color categoryColor;
                                                try {
                                                  categoryColor = Color(
                                                    int.parse(
                                                          category.color
                                                              .substring(1),
                                                          radix: 16,
                                                        ) +
                                                        0xFF000000,
                                                  );
                                                } catch (e) {
                                                  categoryColor = Theme.of(
                                                    context,
                                                  ).colorScheme.primary;
                                                }

                                                return Container(
                                                  padding:
                                                      const EdgeInsets.symmetric(
                                                        horizontal: 8,
                                                        vertical: 4,
                                                      ),
                                                  decoration: BoxDecoration(
                                                    color: categoryColor
                                                        .withValues(alpha: 0.1),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          12,
                                                        ),
                                                    border: Border.all(
                                                      color: categoryColor
                                                          .withValues(
                                                            alpha: 0.3,
                                                          ),
                                                      width: 1,
                                                    ),
                                                  ),
                                                  child: Row(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      Container(
                                                        width: 6,
                                                        height: 6,
                                                        decoration:
                                                            BoxDecoration(
                                                              color:
                                                                  categoryColor,
                                                              shape: BoxShape
                                                                  .circle,
                                                            ),
                                                      ),
                                                      const SizedBox(width: 4),
                                                      Text(
                                                        category.name,
                                                        style: TextStyle(
                                                          fontSize: 11,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          color: categoryColor
                                                              .withValues(
                                                                alpha: 0.8,
                                                              ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                );
                                              }).toList(),
                                            ),
                                          ],
                                        );
                                      }
                                      return const SizedBox.shrink();
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddSimpleDomainScreen(),
            ),
          );
          // Refresh the list when returning from add screen
          _loadSimpleDomains();
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
