import 'dart:convert';
import 'package:http/http.dart' as http;

/// Direct API service that bypasses all complex logic
class DirectApiService {
  static const String baseUrl = 'https://domain.agfgroupindia.com';
  static const String loginEndpoint = '/api/auth/login';

  /// Direct login method with minimal error handling
  static Future<Map<String, dynamic>> directLogin(
    String email,
    String password,
  ) async {
    try {
      final url = Uri.parse('$baseUrl$loginEndpoint');

      final response = await http
          .post(
            url,
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: json.encode({'email': email, 'password': password}),
          )
          .timeout(const Duration(seconds: 30));

      // Handle different response codes
      if (response.statusCode == 200) {
        // Success
        final data = json.decode(response.body);
        return data;
      } else if (response.statusCode == 422) {
        // Invalid credentials
        final data = json.decode(response.body);
        final message = data['message'] ?? 'Invalid credentials';
        throw Exception(message);
      } else if (response.statusCode == 401) {
        // Unauthorized
        throw Exception('Invalid email or password');
      } else if (response.statusCode >= 500) {
        // Server error
        throw Exception('Server error. Please try again later.');
      } else {
        // Other errors
        throw Exception('Login failed. Please try again.');
      }
    } catch (e) {
      // Handle specific error types
      final errorString = e.toString().toLowerCase();

      if (errorString.contains('socketexception')) {
        throw Exception(
          'Network connection failed. Please check your internet connection.',
        );
      } else if (errorString.contains('timeoutexception') ||
          errorString.contains('timeout')) {
        throw Exception('Connection timed out. Please try again.');
      } else if (errorString.contains('formatexception')) {
        throw Exception('Invalid server response. Please try again.');
      } else if (e is Exception) {
        // Re-throw our custom exceptions
        rethrow;
      } else {
        throw Exception('Login failed: ${e.toString()}');
      }
    }
  }
}
