<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ExpirySettings;
use App\Models\Domain;
use App\Mail\DomainExpiryAlert;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class ExpirySettingsController extends Controller
{
    public function index()
    {
        $settings = ExpirySettings::getSettings();
        return view('admin.expiry-settings', compact('settings'));
    }

    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'admin_email' => 'required|email',
            'alert_days' => 'required|string',
            'is_active' => 'boolean',
            'smtp_host' => 'nullable|string',
            'smtp_port' => 'nullable|integer|min:1|max:65535',
            'smtp_username' => 'nullable|string',
            'smtp_password' => 'nullable|string',
            'smtp_encryption' => 'nullable|in:tls,ssl',
            'mail_from_address' => 'nullable|email',
            'mail_from_name' => 'nullable|string'
        ]);

        // Custom validation for alert_days format
        $validator->after(function ($validator) use ($request) {
            $alertDays = $request->input('alert_days');
            if ($alertDays) {
                $days = explode(',', str_replace(' ', '', $alertDays));
                foreach ($days as $day) {
                    if (!is_numeric($day) || intval($day) < 1) {
                        $validator->errors()->add('alert_days', 'Alert days must be comma-separated positive numbers (e.g., 10,20,30).');
                        break;
                    }
                }
            }
        });

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $settings = ExpirySettings::getSettings();
        
        $settings->update([
            'admin_email' => $request->admin_email,
            'alert_days' => $request->alert_days,
            'is_active' => $request->has('is_active'),
            'smtp_host' => $request->smtp_host ?: 'smtp.gmail.com',
            'smtp_port' => $request->smtp_port ?: 587,
            'smtp_username' => $request->smtp_username,
            'smtp_password' => $request->smtp_password,
            'smtp_encryption' => $request->smtp_encryption ?: 'tls',
            'mail_from_address' => $request->mail_from_address ?: $request->admin_email,
            'mail_from_name' => $request->mail_from_name ?: config('app.name')
        ]);

        return redirect()->back()->with('success', 'Expiry settings updated successfully!');
    }

    public function testEmail()
    {
        try {
            $settings = ExpirySettings::getSettings();
            
            if (!$settings->admin_email) {
                return redirect()->back()->with('error', 'Please set an admin email address first.');
            }

            // Update mail configuration
            $settings->updateMailConfig();

            // Get sample domains for testing
            $testDomains = Domain::expiringSoon(30)->take(3)->get();
            
            if ($testDomains->isEmpty()) {
                // Create a test domain entry for demonstration
                $testDomains = collect([
                    (object) [
                        'name' => 'example-test.com',
                        'expiry_date' => Carbon::now()->addDays(15),
                        'days_left' => 15,
                        'category_names' => ['Business'],
                        'rating' => 4,
                        'expiry_status' => 'warning'
                    ]
                ]);
            }

            Mail::to($settings->admin_email)->send(new DomainExpiryAlert($testDomains, 15));

            return redirect()->back()->with('success', 'Test email sent successfully to ' . $settings->admin_email);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to send test email: ' . $e->getMessage());
        }
    }
}
