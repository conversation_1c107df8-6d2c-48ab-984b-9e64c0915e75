import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../utils/network_utils.dart';
import '../utils/theme.dart';

class NetworkStatusWidget extends StatefulWidget {
  final Widget child;
  final bool showPersistentIndicator;
  final VoidCallback? onRetry;

  const NetworkStatusWidget({
    super.key,
    required this.child,
    this.showPersistentIndicator = true,
    this.onRetry,
  });

  @override
  State<NetworkStatusWidget> createState() => _NetworkStatusWidgetState();
}

class _NetworkStatusWidgetState extends State<NetworkStatusWidget> {
  final NetworkUtils _networkUtils = NetworkUtils();
  bool _isConnected = true;
  bool _showBanner = false;

  @override
  void initState() {
    super.initState();
    _initializeNetworkMonitoring();
  }

  void _initializeNetworkMonitoring() {
    _networkUtils.startNetworkMonitoring();
    _networkUtils.networkStatusStream.listen((isConnected) {
      if (mounted) {
        setState(() {
          _isConnected = isConnected;
          _showBanner = !isConnected;
        });
      }
    });

    // Check initial status
    _checkInitialStatus();
  }

  Future<void> _checkInitialStatus() async {
    // Use basic connectivity check to avoid false negatives
    final isConnected = await _networkUtils.hasBasicConnectivity();
    if (mounted) {
      setState(() {
        _isConnected = isConnected;
        _showBanner = !isConnected;
      });
    }
  }

  @override
  void dispose() {
    _networkUtils.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (_showBanner && widget.showPersistentIndicator)
          Positioned(top: 0, left: 0, right: 0, child: _buildNetworkBanner()),
      ],
    );
  }

  Widget _buildNetworkBanner() {
    return Material(
      elevation: 4,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spaceMD,
          vertical: AppTheme.spaceSM,
        ),
        decoration: BoxDecoration(
          color: Colors.red[600],
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: SafeArea(
          bottom: false,
          child: Row(
            children: [
              const Icon(Icons.wifi_off, color: Colors.white, size: 20),
              const SizedBox(width: AppTheme.spaceSM),
              const Expanded(
                child: Text(
                  'No internet connection',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (widget.onRetry != null)
                TextButton(
                  onPressed: () async {
                    await _checkInitialStatus();
                    if (_isConnected && widget.onRetry != null) {
                      widget.onRetry!();
                    }
                  },
                  child: const Text(
                    'Retry',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    ).animate().slideY(begin: -1, end: 0, duration: 300.ms);
  }
}

/// A simple network status indicator that can be embedded anywhere
class NetworkStatusIndicator extends StatefulWidget {
  final bool compact;
  final Color? connectedColor;
  final Color? disconnectedColor;

  const NetworkStatusIndicator({
    super.key,
    this.compact = false,
    this.connectedColor,
    this.disconnectedColor,
  });

  @override
  State<NetworkStatusIndicator> createState() => _NetworkStatusIndicatorState();
}

class _NetworkStatusIndicatorState extends State<NetworkStatusIndicator> {
  final NetworkUtils _networkUtils = NetworkUtils();
  bool _isConnected = true;

  @override
  void initState() {
    super.initState();
    _initializeNetworkMonitoring();
  }

  void _initializeNetworkMonitoring() {
    _networkUtils.startNetworkMonitoring();
    _networkUtils.networkStatusStream.listen((isConnected) {
      if (mounted) {
        setState(() {
          _isConnected = isConnected;
        });
      }
    });

    // Check initial status
    _checkInitialStatus();
  }

  Future<void> _checkInitialStatus() async {
    // Use basic connectivity check to avoid false negatives
    final isConnected = await _networkUtils.hasBasicConnectivity();
    if (mounted) {
      setState(() {
        _isConnected = isConnected;
      });
    }
  }

  @override
  void dispose() {
    _networkUtils.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.compact) {
      return Container(
        width: 8,
        height: 8,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: _isConnected
              ? (widget.connectedColor ?? Colors.green)
              : (widget.disconnectedColor ?? Colors.red),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spaceSM,
        vertical: AppTheme.spaceXS,
      ),
      decoration: BoxDecoration(
        color: _isConnected
            ? (widget.connectedColor ?? Colors.green).withValues(alpha: 0.1)
            : (widget.disconnectedColor ?? Colors.red).withValues(alpha: 0.1),
        borderRadius: AppTheme.smallRadius,
        border: Border.all(
          color: _isConnected
              ? (widget.connectedColor ?? Colors.green).withValues(alpha: 0.3)
              : (widget.disconnectedColor ?? Colors.red).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _isConnected ? Icons.wifi : Icons.wifi_off,
            size: 16,
            color: _isConnected
                ? (widget.connectedColor ?? Colors.green)
                : (widget.disconnectedColor ?? Colors.red),
          ),
          const SizedBox(width: AppTheme.spaceXS),
          Text(
            _isConnected ? 'Online' : 'Offline',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: _isConnected
                  ? (widget.connectedColor ?? Colors.green)
                  : (widget.disconnectedColor ?? Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}

/// Extension to easily wrap any widget with network status monitoring
extension NetworkStatusExtension on Widget {
  Widget withNetworkStatus({
    bool showPersistentIndicator = true,
    VoidCallback? onRetry,
  }) {
    return NetworkStatusWidget(
      showPersistentIndicator: showPersistentIndicator,
      onRetry: onRetry,
      child: this,
    );
  }
}
