@extends('layouts.app')

@section('title', 'Dashboard - Domain CRM')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-chart-pie me-2 text-primary"></i>
        Dashboard Overview
    </h1>
    <div class="d-flex gap-2">
        <button class="btn btn-outline-primary" onclick="refreshAnalytics()">
            <i class="fas fa-sync-alt me-1"></i>
            Refresh
        </button>
        <a href="{{ route('domains.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Add Domain
        </a>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card" style="background: linear-gradient(135deg, #10b981 0%, #34d399 100%); color: white;">
            <div class="card-body text-center">
                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                <h3 class="mb-1">{{ $totalBuyDomains }}</h3>
                <p class="mb-0">Total Purchase</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card" style="background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%); color: white;">
            <div class="card-body text-center">
                <i class="fas fa-bookmark fa-2x mb-2"></i>
                <h3 class="mb-1">{{ $totalReserveDomains }}</h3>
                <p class="mb-0">Total Identify</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card" style="background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%); color: white;">
            <div class="card-body text-center">
                <i class="fas fa-tags fa-2x mb-2"></i>
                <h3 class="mb-1">{{ $totalCategories }}</h3>
                <p class="mb-0">Categories</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card-danger">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h3 class="mb-1">{{ $expiringDomains->count() }}</h3>
                <p class="mb-0">Expiring Soon</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Category Distribution -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Domain Distribution by Category
                </h5>
            </div>
            <div class="card-body">
                @if($categoryStats->count() > 0)
                    @foreach($categoryStats as $stat)
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <span class="badge me-2" style="background-color: {{ $stat['color'] }}">
                                    {{ $stat['name'] }}
                                </span>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold">{{ $stat['total_count'] }}</div>
                                <small class="text-muted">
                                    Buy: {{ $stat['buy_count'] }} | Reserve: {{ $stat['reserve_count'] }}
                                </small>
                            </div>
                        </div>
                        <div class="progress mb-3" style="height: 8px;">
                            <div class="progress-bar" 
                                 style="width: {{ ($totalBuyDomains + $totalReserveDomains) > 0 ? ($stat['total_count'] / ($totalBuyDomains + $totalReserveDomains)) * 100 : 0 }}%; background-color: {{ $stat['color'] }}">
                            </div>
                        </div>
                    @endforeach
                @else
                    <p class="text-muted text-center">No categories found</p>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Expiry Alerts -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                    Expiry Alerts (Next 30 Days)
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="showExpiryModal()">
                    View All
                </button>
            </div>
            <div class="card-body">
                @if($expiringDomains->count() > 0)
                    @foreach($expiringDomains->take(5) as $domain)
                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 rounded" 
                             style="background-color: #fef3c7;">
                            <div>
                                <strong>{{ $domain->name }}</strong>
                                <br>
                                <small class="text-muted">{{ $domain->expiry_date->format('M d, Y') }}</small>
                            </div>
                            <span class="badge bg-warning">
                                {{ $domain->days_left }} days
                            </span>
                        </div>
                    @endforeach
                @else
                    <p class="text-muted text-center">No domains expiring soon</p>
                @endif
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Top Rated Domains -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2 text-warning"></i>
                    Top Rated Domains
                </h5>
            </div>
            <div class="card-body">
                @if($topRatedDomains->count() > 0)
                    @foreach($topRatedDomains->take(5) as $domain)
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <strong>{{ $domain->name }}</strong>
                                <div class="star-rating">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star{{ $i <= $domain->rating ? '' : '-o' }}"></i>
                                    @endfor
                                </div>
                            </div>
                            <div class="text-end">
                                @foreach($domain->category_names as $category)
                                    <span class="badge bg-secondary me-1">{{ $category }}</span>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                @else
                    <p class="text-muted text-center">No top rated domains</p>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Recent Domains -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2 text-info"></i>
                    Recent Activity
                </h5>
            </div>
            <div class="card-body">
                <!-- Recent Buy Domains -->
                @if($recentBuyDomains->count() > 0)
                    <h6 class="text-muted mb-2">
                        <i class="fas fa-shopping-cart me-1"></i>
                        Recent Buy Domains
                    </h6>
                    @foreach($recentBuyDomains->take(3) as $domain)
                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 rounded" style="background-color: #f0f9ff;">
                            <div>
                                <strong>{{ $domain->name }}</strong>
                                <br>
                                <small class="text-muted">{{ $domain->created_at->diffForHumans() }}</small>
                            </div>
                            <div class="text-end">
                                <div class="star-rating">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star{{ $i <= $domain->rating ? '' : '-o' }}"></i>
                                    @endfor
                                </div>
                            </div>
                        </div>
                    @endforeach
                @endif

                <!-- Recent Reserve Domains -->
                @if($recentReserveDomains->count() > 0)
                    <h6 class="text-muted mb-2 mt-3">
                        <i class="fas fa-bookmark me-1"></i>
                        Recent Identify Domains
                    </h6>
                    @foreach($recentReserveDomains->take(3) as $domain)
                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 rounded" style="background-color: #fef3c7;">
                            <div>
                                <strong>{{ $domain->name }}</strong>
                                <br>
                                <small class="text-muted">{{ $domain->created_at->diffForHumans() }}</small>
                            </div>
                            <div class="text-end">
                                @foreach($domain->category_details as $category)
                                    <span class="badge me-1" style="background-color: {{ $category->color }}; font-size: 0.7em;">
                                        {{ $category->name }}
                                    </span>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                @endif

                @if($recentBuyDomains->count() == 0 && $recentReserveDomains->count() == 0)
                    <p class="text-muted text-center">No recent activity</p>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Expiry Modal -->
<div class="modal fade" id="expiryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                    Domains Expiring Soon
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Domain Name</th>
                                <th>Expiry Date</th>
                                <th>Days Left</th>
                                <th>Rating</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($expiringDomains as $domain)
                                <tr>
                                    <td>
                                        <strong>{{ $domain->name }}</strong>
                                        <br>
                                        @foreach($domain->category_names as $category)
                                            <span class="badge bg-secondary me-1">{{ $category }}</span>
                                        @endforeach
                                    </td>
                                    <td>{{ $domain->expiry_date->format('M d, Y') }}</td>
                                    <td>
                                        <span class="badge {{ $domain->days_left <= 7 ? 'bg-danger' : ($domain->days_left <= 15 ? 'bg-warning' : 'bg-info') }}">
                                            {{ $domain->days_left }} days
                                        </span>
                                    </td>
                                    <td>
                                        <div class="star-rating">
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="fas fa-star{{ $i <= $domain->rating ? '' : '-o' }}"></i>
                                            @endfor
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function refreshAnalytics() {
    // Add loading state
    $('body').addClass('loading');
    
    $.get('{{ route("dashboard.analytics") }}')
        .done(function(data) {
            // Update stats cards
            location.reload(); // Simple refresh for now
        })
        .fail(function() {
            alert('Failed to refresh analytics');
        })
        .always(function() {
            $('body').removeClass('loading');
        });
}

function showExpiryModal() {
    $('#expiryModal').modal('show');
}
</script>
@endpush