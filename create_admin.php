<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Admin;

// Check if admin already exists
$existingAdmin = Admin::where('email', '<EMAIL>')->first();

if ($existingAdmin) {
    echo "Admin user already exists!\n";
    echo "Email: " . $existingAdmin->email . "\n";
    echo "Name: " . $existingAdmin->name . "\n";
} else {
    // Create new admin user
    $admin = Admin::create([
        'name' => 'Test Admin',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'role' => 'admin',
        'is_active' => true,
    ]);
    
    echo "Admin user created successfully!\n";
    echo "Email: <EMAIL>\n";
    echo "Password: password\n";
    echo "Name: " . $admin->name . "\n";
}