<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\DomainApiController;
use App\Http\Controllers\Api\SimpleDomainApiController;
use App\Http\Controllers\Api\CategoryApiController;
use App\Http\Controllers\Api\AuthApiController;
use App\Http\Controllers\Api\SettingsApiController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Authentication routes
Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthApiController::class, 'login']);
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', [AuthApiController::class, 'logout']);
        Route::get('/user', [AuthApiController::class, 'user']);
    });
});

// Protected API routes
Route::middleware('auth:sanctum')->group(function () {
    // Categories
    Route::apiResource('categories', CategoryApiController::class);
    Route::get('categories-debug', [CategoryApiController::class, 'debug']);
    
    // Domains
    Route::apiResource('domains', DomainApiController::class);
    Route::post('domains/check-availability', [DomainApiController::class, 'checkAvailability']);
    Route::post('domains/check-duplicate', [DomainApiController::class, 'checkDuplicate']);
    
    // Simple Domains
    Route::apiResource('simple-domains', SimpleDomainApiController::class);
    Route::post('simple-domains/check-duplicate', [SimpleDomainApiController::class, 'checkDuplicate']);
    Route::post('simple-domains/{simpleDomain}/buy', [SimpleDomainApiController::class, 'buy']);

    // Settings
    Route::prefix('settings')->group(function () {
        Route::get('/profile', [SettingsApiController::class, 'getProfile']);
        Route::put('/profile', [SettingsApiController::class, 'updateProfile']);
        Route::get('/expiry', [SettingsApiController::class, 'getExpirySettings']);
        Route::put('/expiry', [SettingsApiController::class, 'updateExpirySettings']);
        Route::post('/expiry/test-email', [SettingsApiController::class, 'testEmail']);
        Route::get('/system', [SettingsApiController::class, 'getSystemSettings']);
    });
    
    // Dashboard stats
    Route::get('dashboard/stats', [DomainApiController::class, 'getDashboardStats']);
});