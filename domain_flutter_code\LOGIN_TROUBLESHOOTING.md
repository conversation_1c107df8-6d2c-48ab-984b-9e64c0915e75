# Flutter Login Troubleshooting Guide

## Common Issues and Solutions

### 1. Connection Issues

**Problem**: App can't connect to Laravel API
**Symptoms**: 
- "No internet connection" error
- "Connection timeout" error
- SocketException errors

**Solutions**:

#### For Android Emulator:
```dart
// In lib/utils/constants.dart, use:
static const String baseUrl = 'http://********:8000/api';
// OR
static const String baseUrl = 'http://127.0.0.1:8000/api';
```

#### For Physical Device:
1. Find your computer's IP address:
   - Windows: `ipconfig`
   - Mac/Linux: `ifconfig` or `ip addr show`
2. Update the base URL:
```dart
static const String baseUrl = 'http://YOUR_IP_ADDRESS:8000/api';
// Example: 'http://*************:8000/api'
```

#### For iOS Simulator:
```dart
static const String baseUrl = 'http://localhost:8000/api';
```

### 2. Laravel Server Issues

**Make sure your Laravel server is running**:
```bash
cd /path/to/your/laravel/project
php artisan serve
```

**Check if the API endpoints are working**:
```bash
# Test the login endpoint
curl -X POST http://127.0.0.1:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'
```

### 3. Database Issues

**Make sure you have an admin user in the database**:
```bash
php artisan tinker
```

Then in tinker:
```php
// Create a test admin user
$admin = new App\Models\Admin();
$admin->name = 'Test Admin';
$admin->email = '<EMAIL>';
$admin->password = bcrypt('password');
$admin->save();
```

### 4. CORS Issues

If you're getting CORS errors, make sure your Laravel app has CORS configured:

1. Install Laravel Sanctum (if not already installed):
```bash
composer require laravel/sanctum
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
php artisan migrate
```

2. Add Sanctum middleware to `app/Http/Kernel.php`:
```php
'api' => [
    \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
    'throttle:api',
    \Illuminate\Routing\Middleware\SubstituteBindings::class,
],
```

### 5. Debug the Flutter App

Add this to your login screen to test the connection:
```dart
import '../utils/debug_helper.dart';

// Add this button to your login screen for testing
ElevatedButton(
  onPressed: () async {
    await DebugHelper.testApiConnection();
  },
  child: Text('Test API Connection'),
),
```

### 6. Check Laravel Logs

Monitor Laravel logs while testing:
```bash
tail -f storage/logs/laravel.log
```

### 7. Network Permissions

Make sure your Flutter app has internet permissions:

**Android** (`android/app/src/main/AndroidManifest.xml`):
```xml
<uses-permission android:name="android.permission.INTERNET" />
```

**iOS** - Usually no additional permissions needed for HTTP requests.

### 8. Test Credentials

Make sure you're using the correct credentials. The default test credentials should be:
- Email: `<EMAIL>`
- Password: `password`

### 9. API Response Format

The Laravel API should return this format for successful login:
```json
{
  "success": true,
  "token": "your-token-here",
  "admin": {
    "id": 1,
    "name": "Admin Name",
    "email": "<EMAIL>",
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  }
}
```

### 10. Flutter Dependencies

Make sure all dependencies are installed:
```bash
cd domain_flutter_code
flutter pub get
```

## Quick Test Steps

1. **Start Laravel server**: `php artisan serve`
2. **Test API manually**: Use curl or Postman to test the login endpoint
3. **Check Flutter logs**: Run `flutter run` and check console output
4. **Update base URL**: Make sure it matches your setup (emulator/device/simulator)
5. **Test with debug helper**: Use the DebugHelper class to test connectivity

## Still Having Issues?

1. Check the Flutter console output for detailed error messages
2. Check Laravel logs for server-side errors
3. Verify your network configuration
4. Try using a different base URL format
5. Test the API with a REST client like Postman first