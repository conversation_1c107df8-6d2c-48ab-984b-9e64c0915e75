<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Domain;
use App\Models\ExpirySettings;
use App\Mail\DomainExpiryAlert;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;

class CheckDomainExpiry extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:check-domain-expiry';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for domains expiring soon and send email alerts';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting domain expiry check...');

        $settings = ExpirySettings::getSettings();

        if (!$settings->is_active) {
            $this->info('Domain expiry alerts are disabled.');
            return;
        }

        if (!$settings->admin_email) {
            $this->error('No admin email configured for alerts.');
            return;
        }

        // Update mail configuration
        $settings->updateMailConfig();

        $alertDays = $settings->alert_days_array;
        $today = Carbon::now();
        $emailsSent = 0;

        foreach ($alertDays as $days) {
            $targetDate = $today->copy()->addDays($days);
            
            // Find domains expiring on this specific day
            $domains = Domain::whereDate('expiry_date', $targetDate->toDateString())
                ->where('expiry_date', '>', $today)
                ->get();

            if ($domains->isNotEmpty()) {
                try {
                    Mail::to($settings->admin_email)->send(new DomainExpiryAlert($domains, $days));
                    $emailsSent++;
                    
                    $this->info("Sent alert for {$domains->count()} domain(s) expiring in {$days} days");
                } catch (\Exception $e) {
                    $this->error("Failed to send alert for domains expiring in {$days} days: " . $e->getMessage());
                }
            }
        }

        if ($emailsSent === 0) {
            $this->info('No domains found requiring alerts today.');
        } else {
            $this->info("Successfully sent {$emailsSent} expiry alert email(s).");
        }
    }
}