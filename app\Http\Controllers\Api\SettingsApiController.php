<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use App\Models\ExpirySettings;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class SettingsApiController extends Controller
{
    /**
     * Get admin profile settings
     */
    public function getProfile()
    {
        $admin = Auth::guard('sanctum')->user();
        
        return response()->json([
            'success' => true,
            'data' => [
                'id' => $admin->id,
                'name' => $admin->name,
                'email' => $admin->email,
                'role' => $admin->role,
                'created_at' => $admin->created_at,
                'updated_at' => $admin->updated_at,
            ]
        ]);
    }

    /**
     * Update admin profile settings
     */
    public function updateProfile(Request $request)
    {
        $admin = Auth::guard('sanctum')->user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:admins,email,' . $admin->id,
            'current_password' => 'nullable|required_with:new_password',
            'new_password' => 'nullable|min:6|confirmed',
        ]);

        // Update basic info
        $admin->name = $request->name;
        $admin->email = $request->email;

        // Update password if provided
        if ($request->filled('new_password')) {
            if (!Hash::check($request->current_password, $admin->password)) {
                throw ValidationException::withMessages([
                    'current_password' => ['Current password is incorrect.'],
                ]);
            }
            $admin->password = Hash::make($request->new_password);
        }

        $admin->save();

        return response()->json([
            'success' => true,
            'message' => 'Profile updated successfully',
            'data' => [
                'id' => $admin->id,
                'name' => $admin->name,
                'email' => $admin->email,
                'role' => $admin->role,
                'updated_at' => $admin->updated_at,
            ]
        ]);
    }

    /**
     * Get expiry settings
     */
    public function getExpirySettings()
    {
        $settings = ExpirySettings::getSettings();
        
        return response()->json([
            'success' => true,
            'data' => [
                'admin_email' => $settings->admin_email,
                'alert_days' => $settings->alert_days,
                'is_active' => $settings->is_active,
                'smtp_host' => $settings->smtp_host,
                'smtp_port' => $settings->smtp_port,
                'smtp_username' => $settings->smtp_username,
                'smtp_password' => $settings->smtp_password ? '••••••••' : '', // Hide password
                'smtp_encryption' => $settings->smtp_encryption,
                'mail_from_address' => $settings->mail_from_address,
                'mail_from_name' => $settings->mail_from_name,
                'created_at' => $settings->created_at,
                'updated_at' => $settings->updated_at,
            ]
        ]);
    }

    /**
     * Update expiry settings
     */
    public function updateExpirySettings(Request $request)
    {
        $request->validate([
            'admin_email' => 'required|email',
            'alert_days' => 'required|string',
            'is_active' => 'boolean',
            'smtp_host' => 'nullable|string',
            'smtp_port' => 'nullable|integer|min:1|max:65535',
            'smtp_username' => 'nullable|string',
            'smtp_password' => 'nullable|string',
            'smtp_encryption' => 'nullable|in:tls,ssl',
            'mail_from_address' => 'nullable|email',
            'mail_from_name' => 'nullable|string'
        ]);

        // Validate alert_days format
        $alertDays = str_replace(' ', '', $request->alert_days);
        $daysArray = explode(',', $alertDays);
        foreach ($daysArray as $day) {
            if (!is_numeric($day) || $day < 1 || $day > 365) {
                throw ValidationException::withMessages([
                    'alert_days' => ['Alert days must be comma-separated numbers between 1 and 365.'],
                ]);
            }
        }

        $settings = ExpirySettings::getSettings();
        
        $updateData = [
            'admin_email' => $request->admin_email,
            'alert_days' => $alertDays,
            'is_active' => $request->boolean('is_active'),
            'smtp_host' => $request->smtp_host ?: 'smtp.gmail.com',
            'smtp_port' => $request->smtp_port ?: 587,
            'smtp_username' => $request->smtp_username,
            'smtp_encryption' => $request->smtp_encryption ?: 'tls',
            'mail_from_address' => $request->mail_from_address ?: $request->admin_email,
            'mail_from_name' => $request->mail_from_name ?: config('app.name')
        ];

        // Only update password if provided
        if ($request->filled('smtp_password')) {
            $updateData['smtp_password'] = $request->smtp_password;
        }

        $settings->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'Expiry settings updated successfully',
            'data' => [
                'admin_email' => $settings->admin_email,
                'alert_days' => $settings->alert_days,
                'is_active' => $settings->is_active,
                'smtp_host' => $settings->smtp_host,
                'smtp_port' => $settings->smtp_port,
                'smtp_username' => $settings->smtp_username,
                'smtp_password' => $settings->smtp_password ? '••••••••' : '',
                'smtp_encryption' => $settings->smtp_encryption,
                'mail_from_address' => $settings->mail_from_address,
                'mail_from_name' => $settings->mail_from_name,
                'updated_at' => $settings->updated_at,
            ]
        ]);
    }

    /**
     * Test email configuration
     */
    public function testEmail(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email'
        ]);

        try {
            $settings = ExpirySettings::getSettings();
            $settings->updateMailConfig();

            // Send test email
            \Mail::raw('This is a test email from Domain CRM. Your email configuration is working correctly!', function ($message) use ($request, $settings) {
                $message->to($request->test_email)
                       ->subject('Domain CRM - Test Email')
                       ->from($settings->mail_from_address ?: $settings->admin_email, $settings->mail_from_name ?: config('app.name'));
            });

            return response()->json([
                'success' => true,
                'message' => 'Test email sent successfully to ' . $request->test_email
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send test email: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get system settings
     */
    public function getSystemSettings()
    {
        $settings = [
            'app_name' => config('app.name', 'Domain CRM'),
            'app_url' => config('app.url'),
            'mail_driver' => config('mail.default'),
            'timezone' => config('app.timezone'),
            'app_env' => config('app.env'),
            'app_debug' => config('app.debug'),
        ];

        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }
}
