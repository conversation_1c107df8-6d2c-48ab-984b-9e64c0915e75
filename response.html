



<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkDuplicateBtn = document.getElementById('checkDuplicateBtn');
    const nameInput = document.getElementById('name');
    const duplicateCheck = document.getElementById('duplicateCheck');

    checkDuplicateBtn.addEventListener('click', function() {
        const domainName = nameInput.value.trim();
        
        if (!domainName) {
            duplicateCheck.innerHTML = '<div class="alert alert-warning alert-sm">Please enter a domain name first.</div>';
            return;
        }

        // Show loading state
        checkDuplicateBtn.disabled = true;
        checkDuplicateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';

        // Make AJAX request to check for duplicates
        fetch('http://127.0.0.1:8000/domains/check-duplicate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ name: domainName })
        })
        .then(response => response.json())
        .then(data => {
            if (data.exists) {
                duplicateCheck.innerHTML = '<div class="alert alert-danger alert-sm"><i class="fas fa-exclamation-triangle me-1"></i>This domain name already exists!</div>';
                nameInput.classList.add('is-invalid');
            } else {
                duplicateCheck.innerHTML = '<div class="alert alert-success alert-sm"><i class="fas fa-check me-1"></i>Domain name is available!</div>';
                nameInput.classList.remove('is-invalid');
            }
        })
        .catch(error => {
            duplicateCheck.innerHTML = '<div class="alert alert-danger alert-sm">Error checking domain. Please try again.</div>';
        })
        .finally(() => {
            // Reset button state
            checkDuplicateBtn.disabled = false;
            checkDuplicateBtn.innerHTML = '<i class="fas fa-search"></i> Check';
        });
    });

    // Clear duplicate check when name changes
    nameInput.addEventListener('input', function() {
        duplicateCheck.innerHTML = '';
        nameInput.classList.remove('is-invalid');
    });
});
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="2uviQ0ExapHRlK0prQK3yVsO85SvpoxuiWrpVAtG">
    <title>Add Domain - Domain CRM</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
        }
        
        body {
            background-color: #f8fafc;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 4px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .table {
            border-radius: 12px;
            overflow: hidden;
        }
        
        .table thead th {
            background-color: #f8fafc;
            border: none;
            font-weight: 600;
            color: #374151;
        }
        
        .badge {
            font-weight: 500;
            padding: 6px 12px;
        }
        
        .star-rating {
            color: #fbbf24;
        }
        
        .domain-card {
            border-left: 4px solid var(--primary-color);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .modal-content {
            border-radius: 12px;
            border: none;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #d1d5db;
            transition: border-color 0.2s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .alert {
            border-radius: 8px;
            border: none;
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
    </head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 p-0">
                <div class="sidebar">
                    <div class="p-4">
                        <h4 class="text-white mb-4">
                            <i class="fas fa-globe me-2"></i>
                            Domain CRM
                        </h4>
                        <nav class="nav flex-column">
                            <a class="nav-link " href="http://127.0.0.1:8000">
                                <i class="fas fa-chart-pie me-2"></i>
                                Dashboard
                            </a>
                            <a class="nav-link active" href="http://127.0.0.1:8000/domains">
                                <i class="fas fa-list me-2"></i>
                                Domains
                            </a>
                            <a class="nav-link " href="http://127.0.0.1:8000/categories">
                                <i class="fas fa-tags me-2"></i>
                                Categories
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-10">
                <div class="p-4">
                                        
                                        
                    <div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-plus me-2 text-primary"></i>
        Add New Domain
    </h1>
    <a href="http://127.0.0.1:8000/domains" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>
        Back to Domains
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form id="domainForm" method="POST" action="http://127.0.0.1:8000/domains">
                    <input type="hidden" name="_token" value="2uviQ0ExapHRlK0prQK3yVsO85SvpoxuiWrpVAtG" autocomplete="off">                    
                    <!-- Domain Name -->
                    <div class="mb-3">
                        <label for="name" class="form-label">Domain Name *</label>
                        <div class="input-group">
                            <input type="text" class="form-control " 
                                   id="name" name="name" value="" 
                                   placeholder="example" required>
                            <button type="button" class="btn btn-outline-primary" id="checkDuplicateBtn">
                                <i class="fas fa-search"></i>
                                Check
                            </button>
                        </div>
                        <div id="duplicateCheck" class="mt-2"></div>
                                            </div>
                    
                    <!-- Extensions -->
                    <div class="mb-3">
                        <label class="form-label">Extensions *</label>
                        <div class="row">
                                                                                        <div class="col-md-3 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="extensions[]" value=".com" 
                                               id="ext0"
                                               >
                                        <label class="form-check-label" for="ext0">
                                            .com
                                        </label>
                                    </div>
                                </div>
                                                            <div class="col-md-3 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="extensions[]" value=".in" 
                                               id="ext1"
                                               >
                                        <label class="form-check-label" for="ext1">
                                            .in
                                        </label>
                                    </div>
                                </div>
                                                            <div class="col-md-3 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="extensions[]" value=".net" 
                                               id="ext2"
                                               >
                                        <label class="form-check-label" for="ext2">
                                            .net
                                        </label>
                                    </div>
                                </div>
                                                            <div class="col-md-3 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="extensions[]" value=".info" 
                                               id="ext3"
                                               >
                                        <label class="form-check-label" for="ext3">
                                            .info
                                        </label>
                                    </div>
                                </div>
                                                            <div class="col-md-3 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="extensions[]" value=".online" 
                                               id="ext4"
                                               >
                                        <label class="form-check-label" for="ext4">
                                            .online
                                        </label>
                                    </div>
                                </div>
                                                            <div class="col-md-3 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="extensions[]" value=".org" 
                                               id="ext5"
                                               >
                                        <label class="form-check-label" for="ext5">
                                            .org
                                        </label>
                                    </div>
                                </div>
                                                            <div class="col-md-3 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="extensions[]" value=".co" 
                                               id="ext6"
                                               >
                                        <label class="form-check-label" for="ext6">
                                            .co
                                        </label>
                                    </div>
                                </div>
                                                            <div class="col-md-3 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="extensions[]" value=".io" 
                                               id="ext7"
                                               >
                                        <label class="form-check-label" for="ext7">
                                            .io
                                        </label>
                                    </div>
                                </div>
                                                    </div>
                                            </div>

                    <!-- Categories -->
                    <div class="mb-3">
                        <label class="form-label">Categories *</label>
                        <div class="row">
                                                                                        <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="categories[]" value="1" 
                                               id="cat1"
                                               >
                                        <label class="form-check-label" for="cat1">
                                            <span class="badge" style="background-color: #3b82f6">
                                                Technology
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                                            <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="categories[]" value="2" 
                                               id="cat2"
                                               >
                                        <label class="form-check-label" for="cat2">
                                            <span class="badge" style="background-color: #10b981">
                                                Business
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                                            <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="categories[]" value="3" 
                                               id="cat3"
                                               >
                                        <label class="form-check-label" for="cat3">
                                            <span class="badge" style="background-color: #f59e0b">
                                                E-commerce
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                                            <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="categories[]" value="4" 
                                               id="cat4"
                                               >
                                        <label class="form-check-label" for="cat4">
                                            <span class="badge" style="background-color: #8b5cf6">
                                                Blog
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                                            <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="categories[]" value="5" 
                                               id="cat5"
                                               >
                                        <label class="form-check-label" for="cat5">
                                            <span class="badge" style="background-color: #ef4444">
                                                Portfolio
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                                            <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="categories[]" value="6" 
                                               id="cat6"
                                               >
                                        <label class="form-check-label" for="cat6">
                                            <span class="badge" style="background-color: #06b6d4">
                                                News
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                                            <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="categories[]" value="7" 
                                               id="cat7"
                                               >
                                        <label class="form-check-label" for="cat7">
                                            <span class="badge" style="background-color: #84cc16">
                                                Education
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                                            <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="categories[]" value="8" 
                                               id="cat8"
                                               >
                                        <label class="form-check-label" for="cat8">
                                            <span class="badge" style="background-color: #f97316">
                                                Entertainment
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                                            <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="categories[]" value="9" 
                                               id="cat9"
                                               >
                                        <label class="form-check-label" for="cat9">
                                            <span class="badge" style="background-color: #2366af">
                                                gfhfghfgh
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                                    </div>
                                            </div>

                    <!-- Expiry Date -->
                    <div class="mb-3">
                        <label for="expiry_date" class="form-label">Expiry Date *</label>
                        <input type="date" class="form-control " 
                               id="expiry_date" name="expiry_date" value="" 
                               min="2025-07-19" required>
                                            </div>

                    <!-- Rating -->
                    <div class="mb-3">
                        <label for="rating" class="form-label">Rating *</label>
                        <select class="form-select " 
                                id="rating" name="rating" required>
                            <option value="">Select Rating</option>
                                                            <option value="1" >
                                    1 Star
                                </option>
                                                            <option value="2" >
                                    2 Stars
                                </option>
                                                            <option value="3" >
                                    3 Stars
                                </option>
                                                            <option value="4" >
                                    4 Stars
                                </option>
                                                            <option value="5" >
                                    5 Stars
                                </option>
                                                    </select>
                                            </div>

                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-end gap-2">
                        <a href="http://127.0.0.1:8000/domains" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Save Domain
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <script>
        // CSRF token setup for AJAX
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        
        // Auto-hide alerts
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    </script>
    
    </body>
</html>