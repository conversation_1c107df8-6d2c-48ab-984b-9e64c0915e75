import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/simple_domain.dart';
import '../providers/domain_provider.dart';
import '../utils/date_formatter.dart';

class BuyDomainDialog extends StatefulWidget {
  final SimpleDomain domain;

  const BuyDomainDialog({super.key, required this.domain});

  @override
  State<BuyDomainDialog> createState() => _BuyDomainDialogState();
}

class _BuyDomainDialogState extends State<BuyDomainDialog> {
  final _formKey = GlobalKey<FormState>();
  late DateTime _expiryDate;
  int _rating = 3;

  // Extension management
  List<String> _selectedExtensions = [];
  List<String> _customExtensions = [];
  final TextEditingController _customExtensionController =
      TextEditingController();
  final List<String> _commonExtensions = ['.com', '.in'];

  @override
  void initState() {
    super.initState();
    _expiryDate = DateTime.now().add(const Duration(days: 365));
  }

  @override
  void dispose() {
    _customExtensionController.dispose();
    super.dispose();
  }

  void _toggleExtension(String extension) {
    setState(() {
      if (_selectedExtensions.contains(extension)) {
        _selectedExtensions.remove(extension);
      } else {
        _selectedExtensions.add(extension);
      }
    });
  }

  void _addCustomExtension() {
    final extension = _customExtensionController.text.trim();
    if (extension.isNotEmpty && !extension.startsWith('.')) {
      final formattedExtension = '.$extension';
      if (!_customExtensions.contains(formattedExtension) &&
          !_commonExtensions.contains(formattedExtension)) {
        setState(() {
          _customExtensions.add(formattedExtension);
          _selectedExtensions.add(formattedExtension);
          _customExtensionController.clear();
        });
      }
    } else if (extension.startsWith('.') && extension.length > 1) {
      if (!_customExtensions.contains(extension) &&
          !_commonExtensions.contains(extension)) {
        setState(() {
          _customExtensions.add(extension);
          _selectedExtensions.add(extension);
          _customExtensionController.clear();
        });
      }
    }
  }

  void _removeCustomExtension(String extension) {
    setState(() {
      _customExtensions.remove(extension);
      _selectedExtensions.remove(extension);
    });
  }

  Future<void> _buyDomain() async {
    if (_formKey.currentState!.validate()) {
      if (_selectedExtensions.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select at least one extension'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final domainProvider = Provider.of<DomainProvider>(
        context,
        listen: false,
      );

      final data = {
        'extensions': _selectedExtensions,
        'expiry_date': _expiryDate.toIso8601String().split(
          'T',
        )[0], // Format as YYYY-MM-DD
        'rating': _rating,
      };

      final success = await domainProvider.buySimpleDomain(
        widget.domain.id,
        data,
      );

      if (mounted) {
        if (success) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Domain purchased successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                domainProvider.error ?? 'Failed to purchase domain',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 700),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.green,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.shopping_cart_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Complete Purchase',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Buy "${widget.domain.name}"',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Info Card
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Colors.blue.shade700,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'Complete the information below to purchase this domain. The reserved domain will be converted to a full domain.',
                                style: TextStyle(
                                  color: Colors.blue.shade700,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Extensions Section
                      Text(
                        'Domain Extensions',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Select one or more extensions for your domain',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Common Extensions
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: _commonExtensions.map((extension) {
                          final isSelected = _selectedExtensions.contains(
                            extension,
                          );
                          return FilterChip(
                            label: Text(extension),
                            selected: isSelected,
                            onSelected: (selected) =>
                                _toggleExtension(extension),
                            backgroundColor: Theme.of(
                              context,
                            ).colorScheme.surfaceContainerHighest,
                            selectedColor: Colors.green,
                            checkmarkColor: Colors.white,
                          );
                        }).toList(),
                      ),

                      // Custom Extensions
                      if (_customExtensions.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: _customExtensions.map((extension) {
                            final isSelected = _selectedExtensions.contains(
                              extension,
                            );
                            return FilterChip(
                              label: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(extension),
                                  const SizedBox(width: 4),
                                  GestureDetector(
                                    onTap: () =>
                                        _removeCustomExtension(extension),
                                    child: const Icon(
                                      Icons.close,
                                      size: 16,
                                      color: Colors.red,
                                    ),
                                  ),
                                ],
                              ),
                              selected: isSelected,
                              onSelected: (selected) =>
                                  _toggleExtension(extension),
                              backgroundColor: Colors.orange.shade100,
                              selectedColor: Colors.orange.shade300,
                            );
                          }).toList(),
                        ),
                      ],

                      const SizedBox(height: 12),

                      // Add Custom Extension
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _customExtensionController,
                              decoration: InputDecoration(
                                hintText: 'Add custom extension (e.g., xyz)',
                                prefixText: '.',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                isDense: true,
                              ),
                              onSubmitted: (_) => _addCustomExtension(),
                            ),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: _addCustomExtension,
                            child: const Text('Add'),
                          ),
                        ],
                      ),

                      const SizedBox(height: 20),

                      // Expiry Date Section
                      Text(
                        'Expiry Date',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 8),
                      InkWell(
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _expiryDate,
                            firstDate: DateTime.now(),
                            lastDate: DateTime.now().add(
                              const Duration(days: 3650),
                            ),
                          );
                          if (date != null) {
                            setState(() {
                              _expiryDate = date;
                            });
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.calendar_today, size: 20),
                              const SizedBox(width: 8),
                              Text(
                                DateFormatter.formatDateFromDateTime(
                                  _expiryDate,
                                ),
                              ),
                              const Spacer(),
                              const Icon(Icons.arrow_drop_down),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Rating Section
                      Text(
                        'Domain Rating',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          ...List.generate(5, (index) {
                            return GestureDetector(
                              onTap: () {
                                setState(() {
                                  _rating = index + 1;
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                margin: const EdgeInsets.only(right: 4),
                                child: Icon(
                                  Icons.star,
                                  color: index < _rating
                                      ? Colors.amber
                                      : Colors.grey.shade300,
                                  size: 32,
                                ),
                              ),
                            );
                          }),
                          const SizedBox(width: 8),
                          Text(
                            '$_rating/5',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.amber.shade700,
                                ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Action Buttons
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(
                  context,
                ).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    flex: 2,
                    child: Consumer<DomainProvider>(
                      builder: (context, domainProvider, child) {
                        return Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            gradient: domainProvider.isLoading
                                ? const LinearGradient(
                                    colors: [
                                      Color(0xFF9E9E9E),
                                      Color(0xFF757575),
                                    ],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  )
                                : const LinearGradient(
                                    colors: [
                                      Color(0xFF4CAF50),
                                      Color(0xFF45A049),
                                    ],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ),
                            boxShadow: domainProvider.isLoading
                                ? []
                                : [
                                    BoxShadow(
                                      color: const Color(
                                        0xFF4CAF50,
                                      ).withValues(alpha: 0.4),
                                      blurRadius: 10,
                                      offset: const Offset(0, 5),
                                    ),
                                  ],
                          ),
                          child: ElevatedButton.icon(
                            onPressed: domainProvider.isLoading
                                ? null
                                : _buyDomain,
                            icon: domainProvider.isLoading
                                ? const SizedBox(
                                    height: 18,
                                    width: 18,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  )
                                : const Icon(
                                    Icons.shopping_cart_checkout_rounded,
                                    size: 20,
                                  ),
                            label: Text(
                              domainProvider.isLoading
                                  ? 'Processing Purchase...'
                                  : 'Complete Purchase',
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              foregroundColor: Colors.white,
                              shadowColor: Colors.transparent,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 0,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
