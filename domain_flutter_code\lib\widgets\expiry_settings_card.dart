import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';

class ExpirySettingsCard extends StatefulWidget {
  const ExpirySettingsCard({super.key});

  @override
  State<ExpirySettingsCard> createState() => _ExpirySettingsCardState();
}

class _ExpirySettingsCardState extends State<ExpirySettingsCard> {
  final _formKey = GlobalKey<FormState>();
  final _adminEmailController = TextEditingController();
  final _alertDaysController = TextEditingController();
  final _smtpHostController = TextEditingController();
  final _smtpPortController = TextEditingController();
  final _smtpUsernameController = TextEditingController();
  final _smtpPasswordController = TextEditingController();
  final _mailFromAddressController = TextEditingController();
  final _mailFromNameController = TextEditingController();
  final _testEmailController = TextEditingController();

  bool _isActive = false;
  String _smtpEncryption = 'tls';
  bool _obscurePassword = true;
  bool _isEditing = false;
  bool _showAdvancedSettings = false;

  @override
  void initState() {
    super.initState();
    _loadSettingsData();
  }

  @override
  void dispose() {
    _adminEmailController.dispose();
    _alertDaysController.dispose();
    _smtpHostController.dispose();
    _smtpPortController.dispose();
    _smtpUsernameController.dispose();
    _smtpPasswordController.dispose();
    _mailFromAddressController.dispose();
    _mailFromNameController.dispose();
    _testEmailController.dispose();
    super.dispose();
  }

  void _loadSettingsData() {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final settings = settingsProvider.expirySettings;
    if (settings != null) {
      _adminEmailController.text = settings.adminEmail;
      _alertDaysController.text = settings.alertDays;
      _isActive = settings.isActive;
      _smtpHostController.text = settings.smtpHost ?? 'smtp.gmail.com';
      _smtpPortController.text = (settings.smtpPort ?? 587).toString();
      _smtpUsernameController.text = settings.smtpUsername ?? '';
      _smtpPasswordController.text = settings.smtpPassword ?? '';
      _smtpEncryption = settings.smtpEncryption ?? 'tls';
      _mailFromAddressController.text =
          settings.mailFromAddress ?? settings.adminEmail;
      _mailFromNameController.text = settings.mailFromName ?? 'Domain CRM';
    }
  }

  Future<void> _updateSettings() async {
    if (!_formKey.currentState!.validate()) return;

    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );

    final data = {
      'admin_email': _adminEmailController.text.trim(),
      'alert_days': _alertDaysController.text.trim(),
      'is_active': _isActive,
      'smtp_host': _smtpHostController.text.trim(),
      'smtp_port': int.tryParse(_smtpPortController.text.trim()) ?? 587,
      'smtp_username': _smtpUsernameController.text.trim(),
      'smtp_password': _smtpPasswordController.text.trim(),
      'smtp_encryption': _smtpEncryption,
      'mail_from_address': _mailFromAddressController.text.trim(),
      'mail_from_name': _mailFromNameController.text.trim(),
    };

    final success = await settingsProvider.updateExpirySettings(data);

    if (mounted) {
      if (success) {
        setState(() {
          _isEditing = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Expiry settings updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              settingsProvider.error ?? 'Failed to update settings',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _testEmail() async {
    if (_testEmailController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a test email address'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final success = await settingsProvider.testEmail(
      _testEmailController.text.trim(),
    );

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Test email sent to ${_testEmailController.text.trim()}',
            ),
            backgroundColor: Colors.green,
          ),
        );
        _testEmailController.clear();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              settingsProvider.error ?? 'Failed to send test email',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _cancelEdit() {
    setState(() {
      _isEditing = false;
      _loadSettingsData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settingsProvider, child) {
        final settings = settingsProvider.expirySettings;

        if (settings == null) {
          return Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Center(
              child: Text('No expiry settings data available'),
            ),
          );
        }

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Theme.of(
                  context,
                ).colorScheme.shadow.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Current Settings Display
                if (!_isEditing) ...[
                  // Status Card
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: settings.isActive
                          ? Colors.green.withValues(alpha: 0.1)
                          : Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: settings.isActive
                            ? Colors.green.withValues(alpha: 0.3)
                            : Colors.red.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          settings.isActive ? Icons.check_circle : Icons.cancel,
                          color: settings.isActive ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                settings.isActive
                                    ? 'Alerts Enabled'
                                    : 'Alerts Disabled',
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: settings.isActive
                                          ? Colors.green
                                          : Colors.red,
                                    ),
                              ),
                              Text(
                                settings.isActive
                                    ? 'Domain expiry alerts are active'
                                    : 'Domain expiry alerts are disabled',
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Settings Summary
                  _buildInfoRow(
                    'Admin Email',
                    settings.adminEmail,
                    Icons.email,
                  ),
                  _buildInfoRow(
                    'Alert Days',
                    settings.alertDaysDisplay,
                    Icons.schedule,
                  ),
                  _buildInfoRow(
                    'SMTP Status',
                    settings.hasSmtpConfig ? 'Configured' : 'Not Configured',
                    settings.hasSmtpConfig ? Icons.check_circle : Icons.warning,
                  ),

                  const SizedBox(height: 24),

                  // Action Buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            setState(() {
                              _isEditing = true;
                            });
                          },
                          icon: const Icon(Icons.edit_rounded),
                          label: const Text('Edit Settings'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _showTestEmailDialog(),
                          icon: const Icon(Icons.send_rounded),
                          label: const Text('Test Email'),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],

                // Edit Form
                if (_isEditing) ...[
                  Row(
                    children: [
                      Icon(
                        Icons.notifications_rounded,
                        color: Colors.orange,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Alert Settings',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.w600),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Enable/Disable Switch
                  SwitchListTile(
                    title: const Text('Enable Expiry Alerts'),
                    subtitle: const Text(
                      'Send email notifications for domain expiry',
                    ),
                    value: _isActive,
                    onChanged: (value) {
                      setState(() {
                        _isActive = value;
                      });
                    },
                    activeColor: Colors.green,
                  ),
                  const SizedBox(height: 16),

                  // Admin Email
                  TextFormField(
                    controller: _adminEmailController,
                    decoration: InputDecoration(
                      labelText: 'Admin Email',
                      prefixIcon: const Icon(Icons.email_outlined),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Theme.of(context)
                          .colorScheme
                          .surfaceContainerHighest
                          .withValues(alpha: 0.3),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter admin email';
                      }
                      if (!RegExp(
                        r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                      ).hasMatch(value)) {
                        return 'Please enter a valid email';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Alert Days
                  TextFormField(
                    controller: _alertDaysController,
                    decoration: InputDecoration(
                      labelText: 'Alert Days',
                      hintText: '30,15,7,1',
                      prefixIcon: const Icon(Icons.schedule_outlined),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Theme.of(context)
                          .colorScheme
                          .surfaceContainerHighest
                          .withValues(alpha: 0.3),
                      helperText:
                          'Comma-separated days before expiry (e.g., 30,15,7,1)',
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter alert days';
                      }
                      final days = value.split(',');
                      for (String day in days) {
                        final dayNum = int.tryParse(day.trim());
                        if (dayNum == null || dayNum < 1 || dayNum > 365) {
                          return 'Days must be numbers between 1 and 365';
                        }
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),

                  // SMTP Settings Header
                  ExpansionTile(
                    title: Row(
                      children: [
                        Icon(
                          Icons.mail_outline_rounded,
                          color: Theme.of(context).colorScheme.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'SMTP Configuration',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.w600),
                        ),
                      ],
                    ),
                    initiallyExpanded: _showAdvancedSettings,
                    onExpansionChanged: (expanded) {
                      setState(() {
                        _showAdvancedSettings = expanded;
                      });
                    },
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          children: [
                            // Info Card
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.blue.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.blue.withValues(alpha: 0.3),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.info_outline,
                                    color: Colors.blue,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      'For Gmail: Enable 2-Step Verification and use App Password',
                                      style: TextStyle(
                                        color: Colors.blue.shade700,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 16),

                            // SMTP Host and Port
                            Row(
                              children: [
                                Expanded(
                                  flex: 2,
                                  child: TextFormField(
                                    controller: _smtpHostController,
                                    decoration: InputDecoration(
                                      labelText: 'SMTP Host',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      filled: true,
                                      fillColor: Theme.of(context)
                                          .colorScheme
                                          .surfaceContainerHighest
                                          .withValues(alpha: 0.3),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: TextFormField(
                                    controller: _smtpPortController,
                                    decoration: InputDecoration(
                                      labelText: 'Port',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      filled: true,
                                      fillColor: Theme.of(context)
                                          .colorScheme
                                          .surfaceContainerHighest
                                          .withValues(alpha: 0.3),
                                    ),
                                    keyboardType: TextInputType.number,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // SMTP Username
                            TextFormField(
                              controller: _smtpUsernameController,
                              decoration: InputDecoration(
                                labelText: 'SMTP Username',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                filled: true,
                                fillColor: Theme.of(context)
                                    .colorScheme
                                    .surfaceContainerHighest
                                    .withValues(alpha: 0.3),
                              ),
                            ),
                            const SizedBox(height: 16),

                            // SMTP Password
                            TextFormField(
                              controller: _smtpPasswordController,
                              obscureText: _obscurePassword,
                              decoration: InputDecoration(
                                labelText: 'SMTP Password',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                filled: true,
                                fillColor: Theme.of(context)
                                    .colorScheme
                                    .surfaceContainerHighest
                                    .withValues(alpha: 0.3),
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _obscurePassword
                                        ? Icons.visibility_off
                                        : Icons.visibility,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _obscurePassword = !_obscurePassword;
                                    });
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Action Buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _cancelEdit,
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        flex: 2,
                        child: ElevatedButton.icon(
                          onPressed: settingsProvider.isLoading
                              ? null
                              : _updateSettings,
                          icon: settingsProvider.isLoading
                              ? const SizedBox(
                                  height: 16,
                                  width: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Icon(Icons.save_rounded),
                          label: Text(
                            settingsProvider.isLoading
                                ? 'Updating...'
                                : 'Update Settings',
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 18, color: Theme.of(context).colorScheme.primary),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
                Text(
                  value,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showTestEmailDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Test Email Configuration'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Enter an email address to test the email configuration:',
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _testEmailController,
              decoration: const InputDecoration(
                labelText: 'Test Email Address',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _testEmail();
            },
            child: const Text('Send Test'),
          ),
        ],
      ),
    );
  }
}
