class Domain {
  final int id;
  final String name;
  final List<String> extensions;
  final List<int> categories;
  final String expiryDate;
  final int rating;
  final bool? isAvailable;
  final String? availabilityCheckedAt;
  final String createdAt;
  final String updatedAt;
  final int? daysLeft;
  final List<String>? categoryNames;
  final String? expiryStatus;
  final String? availabilityStatus;

  Domain({
    required this.id,
    required this.name,
    required this.extensions,
    required this.categories,
    required this.expiryDate,
    required this.rating,
    this.isAvailable,
    this.availabilityCheckedAt,
    required this.createdAt,
    required this.updatedAt,
    this.daysLeft,
    this.categoryNames,
    this.expiryStatus,
    this.availabilityStatus,
  });

  factory Domain.fromJson(Map<String, dynamic> json) {
    return Domain(
      id: _parseInt(json['id']),
      name: json['name']?.toString() ?? '',
      extensions: _parseStringList(json['extensions']),
      categories: _parseIntList(json['categories']),
      expiryDate: json['expiry_date']?.toString() ?? '',
      rating: _parseInt(json['rating']),
      isAvailable: json['is_available'] as bool?,
      availabilityCheckedAt: json['availability_checked_at']?.toString(),
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
      daysLeft: json['days_left'] != null ? _parseInt(json['days_left']) : null,
      categoryNames: json['category_names'] != null 
          ? _parseStringList(json['category_names'])
          : null,
      expiryStatus: json['expiry_status']?.toString(),
      availabilityStatus: json['availability_status']?.toString(),
    );
  }

  static int _parseInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }

  static List<String> _parseStringList(dynamic value) {
    if (value == null) return [];
    if (value is List) return value.map((e) => e.toString()).toList();
    return [];
  }

  static List<int> _parseIntList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.map((e) {
        if (e is int) return e;
        if (e is String) return int.tryParse(e) ?? 0;
        return 0;
      }).toList();
    }
    return [];
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'extensions': extensions,
      'categories': categories,
      'expiry_date': expiryDate,
      'rating': rating,
      'is_available': isAvailable,
      'availability_checked_at': availabilityCheckedAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'days_left': daysLeft,
      'category_names': categoryNames,
      'expiry_status': expiryStatus,
      'availability_status': availabilityStatus,
    };
  }
}