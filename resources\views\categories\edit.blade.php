@extends('layouts.app')

@section('title', 'Edit Category - Domain CRM')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-edit me-2 text-primary"></i>
        Edit Category
    </h1>
    <a href="{{ route('categories.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>
        Back to Categories
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form action="{{ route('categories.update', $category) }}" method="POST" id="categoryForm">
                    @csrf
                    @method('PUT')
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Category Name</label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                               id="name" name="name" value="{{ old('name', $category->name) }}" required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="color" class="form-label">Category Color</label>
                        <div class="input-group">
                            <input type="color" class="form-control form-control-color @error('color') is-invalid @enderror" 
                                   id="color" name="color" value="{{ old('color', $category->color) }}" required>
                            <input type="text" class="form-control @error('color') is-invalid @enderror" 
                                   id="colorText" value="{{ old('color', $category->color) }}" readonly>
                        </div>
                        @error('color')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">Choose a color to represent this category</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Preview</label>
                        <div>
                            <span id="categoryPreview" class="badge" style="background-color: {{ old('color', $category->color) }}; color: white; font-size: 14px;">
                                {{ old('name', $category->name) }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ route('categories.index') }}" class="btn btn-outline-secondary">
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Update Category
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Update color text and preview when color picker changes
    $('#color').on('input', function() {
        const color = $(this).val();
        $('#colorText').val(color);
        updatePreview();
    });
    
    // Update preview when name changes
    $('#name').on('input', function() {
        updatePreview();
    });
    
    function updatePreview() {
        const name = $('#name').val() || 'Category Name';
        const color = $('#color').val();
        
        $('#categoryPreview').text(name).css('background-color', color);
    }
});
</script>
@endpush