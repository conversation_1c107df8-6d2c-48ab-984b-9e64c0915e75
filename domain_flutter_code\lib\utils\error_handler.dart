import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class ErrorHandler {
  static const Map<String, String> _errorMessages = {
    // Network errors
    'socketexception':
        'Unable to connect to server. Please check your internet connection.',
    'no internet connection':
        'Unable to connect to server. Please check your internet connection.',
    'network is unreachable': 'Server is unreachable. Please try again later.',
    'connection refused': 'Server is not responding. Please try again later.',
    'failed to connect': 'Unable to connect to server. Please try again later.',
    'connection reset': 'Connection was reset. Please try again.',
    'connection aborted': 'Connection was aborted. Please try again.',
    'connection timed out':
        'Connection timed out. Please check your internet connection.',
    'no route to host':
        'Cannot reach server. Please check your network settings.',
    'temporary failure in name resolution':
        'DNS resolution failed. Please check your internet connection.',
    'connection closed':
        'Connection was closed unexpectedly. Please try again.',
    'broken pipe': 'Connection error occurred. Please try again.',

    // Timeout errors
    'timeoutexception': 'Request timed out. Please try again.',
    'timeout': 'Request timed out. Please try again.',

    // HTTP errors
    '400': 'Bad request. Please check your input and try again.',
    '401': 'Invalid credentials. Please check your email and password.',
    '403': 'Access denied. Please contact support if this persists.',
    '404': 'Service not found. Please try again later.',
    '422': 'Invalid data provided. Please check your input.',
    '429': 'Too many requests. Please wait a moment and try again.',
    '500': 'Server error. Please try again later.',
    '502': 'Server temporarily unavailable. Please try again later.',
    '503': 'Service unavailable. Please try again later.',
    '504': 'Gateway timeout. Please try again later.',

    // Authentication errors
    'unauthorized':
        'Invalid credentials. Please check your email and password.',
    'forbidden': 'Access denied. Please contact support.',
    'session expired': 'Your session has expired. Please login again.',
    'invalid token': 'Authentication failed. Please login again.',

    // Validation errors
    'validation': 'Please check your input and try again.',
    'invalid email': 'Please enter a valid email address.',
    'invalid password': 'Password must be at least 6 characters long.',
    'required field': 'Please fill in all required fields.',

    // JSON/Format errors
    'formatexception': 'Invalid server response. Please try again.',
    'invalid json': 'Invalid server response. Please try again.',
    'json decode': 'Server response error. Please try again.',

    // Generic errors
    'unknown error': 'An unexpected error occurred. Please try again.',
    'server error': 'Server error occurred. Please try again later.',
  };

  /// Get user-friendly error message from exception or error string
  static String getErrorMessage(dynamic error) {
    if (error == null) return 'An unexpected error occurred.';

    String errorString = error.toString().toLowerCase();

    // Clean up error message
    if (errorString.startsWith('exception: ')) {
      errorString = errorString.substring(11);
    }

    // Check for specific error patterns
    for (final entry in _errorMessages.entries) {
      if (errorString.contains(entry.key)) {
        return entry.value;
      }
    }

    // If no specific pattern found, return cleaned up original message
    String originalMessage = error.toString();
    if (originalMessage.startsWith('Exception: ')) {
      originalMessage = originalMessage.substring(11);
    }

    // Capitalize first letter
    if (originalMessage.isNotEmpty) {
      originalMessage =
          originalMessage[0].toUpperCase() + originalMessage.substring(1);
    }

    return originalMessage.isNotEmpty
        ? originalMessage
        : 'An unexpected error occurred.';
  }

  /// Check if error is network-related
  static bool isNetworkError(dynamic error) {
    if (error == null) return false;

    final errorString = error.toString().toLowerCase();

    return errorString.contains('socketexception') ||
        errorString.contains('no internet connection') ||
        errorString.contains('network is unreachable') ||
        errorString.contains('connection refused') ||
        errorString.contains('failed to connect') ||
        errorString.contains('connection reset') ||
        errorString.contains('connection aborted') ||
        errorString.contains('connection timed out') ||
        errorString.contains('no route to host') ||
        errorString.contains('temporary failure in name resolution') ||
        errorString.contains('connection closed') ||
        errorString.contains('broken pipe');
  }

  /// Check if error is timeout-related
  static bool isTimeoutError(dynamic error) {
    if (error == null) return false;

    final errorString = error.toString().toLowerCase();

    return errorString.contains('timeoutexception') ||
        errorString.contains('timeout') ||
        errorString.contains('timed out');
  }

  /// Check if error is authentication-related
  static bool isAuthError(dynamic error) {
    if (error == null) return false;

    final errorString = error.toString().toLowerCase();

    return errorString.contains('401') ||
        errorString.contains('unauthorized') ||
        errorString.contains('403') ||
        errorString.contains('forbidden') ||
        errorString.contains('session expired') ||
        errorString.contains('invalid token');
  }

  /// Check if error is server-related
  static bool isServerError(dynamic error) {
    if (error == null) return false;

    final errorString = error.toString().toLowerCase();

    return errorString.contains('500') ||
        errorString.contains('502') ||
        errorString.contains('503') ||
        errorString.contains('504') ||
        errorString.contains('internal server error') ||
        errorString.contains('server error');
  }

  /// Get error category for analytics or logging
  static String getErrorCategory(dynamic error) {
    if (isNetworkError(error)) return 'network';
    if (isTimeoutError(error)) return 'timeout';
    if (isAuthError(error)) return 'authentication';
    if (isServerError(error)) return 'server';
    return 'unknown';
  }

  /// Log error for debugging (only in debug mode)
  static void logError(
    dynamic error, {
    String? context,
    StackTrace? stackTrace,
  }) {
    if (kDebugMode) {
      print('=== ERROR LOG ===');
      if (context != null) print('Context: $context');
      print('Error: $error');
      print('Category: ${getErrorCategory(error)}');
      print('User Message: ${getErrorMessage(error)}');
      if (stackTrace != null) print('Stack Trace: $stackTrace');
      print('================');
    }
  }

  /// Show error dialog with retry option
  static Future<bool?> showErrorDialog(
    BuildContext context,
    dynamic error, {
    String? title,
    VoidCallback? onRetry,
    bool showRetry = true,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title ?? 'Error'),
        content: Text(getErrorMessage(error)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('OK'),
          ),
          if (showRetry && onRetry != null)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(true);
                onRetry();
              },
              child: const Text('Retry'),
            ),
        ],
      ),
    );
  }

  /// Show error snackbar
  static void showErrorSnackBar(
    BuildContext context,
    dynamic error, {
    Duration duration = const Duration(seconds: 4),
    VoidCallback? onRetry,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(getErrorMessage(error))),
          ],
        ),
        backgroundColor: Colors.red,
        duration: duration,
        action: onRetry != null
            ? SnackBarAction(
                label: 'Retry',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
      ),
    );
  }
}
