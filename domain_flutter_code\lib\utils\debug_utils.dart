import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:http/http.dart' as http;
import 'network_utils.dart';
import 'constants.dart';

class DebugUtils {
  static final NetworkUtils _networkUtils = NetworkUtils();

  /// Comprehensive network debug information
  static Future<Map<String, dynamic>> getNetworkDebugInfo() async {
    final debugInfo = <String, dynamic>{};
    
    try {
      // Basic connectivity check
      final connectivityResults = await Connectivity().checkConnectivity();
      debugInfo['connectivity_results'] = connectivityResults.map((e) => e.name).toList();
      
      // Basic connectivity
      final hasBasic = await _networkUtils.hasBasicConnectivity();
      debugInfo['has_basic_connectivity'] = hasBasic;
      
      // Internet connectivity
      final hasInternet = await _networkUtils.isConnectedToInternet();
      debugInfo['has_internet_connectivity'] = hasInternet;
      
      // Test specific endpoints
      debugInfo['endpoint_tests'] = await _testEndpoints();
      
      // API specific tests
      debugInfo['api_tests'] = await _testApiEndpoints();
      
      debugInfo['timestamp'] = DateTime.now().toIso8601String();
      debugInfo['status'] = 'success';
      
    } catch (e) {
      debugInfo['error'] = e.toString();
      debugInfo['status'] = 'error';
      debugInfo['timestamp'] = DateTime.now().toIso8601String();
    }
    
    return debugInfo;
  }

  /// Test various endpoints for connectivity
  static Future<Map<String, dynamic>> _testEndpoints() async {
    final results = <String, dynamic>{};
    
    final endpoints = [
      'https://www.google.com',
      'https://dns.google',
      'https://1.1.1.1',
      'https://httpbin.org/status/200',
    ];
    
    for (final endpoint in endpoints) {
      try {
        final stopwatch = Stopwatch()..start();
        final response = await http.head(Uri.parse(endpoint))
            .timeout(const Duration(seconds: 5));
        stopwatch.stop();
        
        results[endpoint] = {
          'status_code': response.statusCode,
          'response_time_ms': stopwatch.elapsedMilliseconds,
          'success': response.statusCode >= 200 && response.statusCode < 500,
        };
      } catch (e) {
        results[endpoint] = {
          'error': e.toString(),
          'success': false,
        };
      }
    }
    
    return results;
  }

  /// Test API specific endpoints
  static Future<Map<String, dynamic>> _testApiEndpoints() async {
    final results = <String, dynamic>{};
    
    final apiEndpoints = [
      '${ApiConstants.baseUrl}',
      '${ApiConstants.baseUrl}/api',
      '${ApiConstants.baseUrl}${ApiConstants.loginEndpoint}',
    ];
    
    for (final endpoint in apiEndpoints) {
      try {
        final stopwatch = Stopwatch()..start();
        final response = await http.head(Uri.parse(endpoint))
            .timeout(const Duration(seconds: 10));
        stopwatch.stop();
        
        results[endpoint] = {
          'status_code': response.statusCode,
          'response_time_ms': stopwatch.elapsedMilliseconds,
          'success': response.statusCode >= 200 && response.statusCode < 500,
          'headers': response.headers,
        };
      } catch (e) {
        results[endpoint] = {
          'error': e.toString(),
          'success': false,
        };
      }
    }
    
    return results;
  }

  /// Print debug information to console
  static Future<void> printNetworkDebugInfo() async {
    if (!kDebugMode) return;
    
    print('=== NETWORK DEBUG INFO ===');
    final info = await getNetworkDebugInfo();
    
    print('Timestamp: ${info['timestamp']}');
    print('Status: ${info['status']}');
    
    if (info['error'] != null) {
      print('Error: ${info['error']}');
      print('========================');
      return;
    }
    
    print('Connectivity Results: ${info['connectivity_results']}');
    print('Has Basic Connectivity: ${info['has_basic_connectivity']}');
    print('Has Internet Connectivity: ${info['has_internet_connectivity']}');
    
    print('\n--- Endpoint Tests ---');
    final endpointTests = info['endpoint_tests'] as Map<String, dynamic>;
    endpointTests.forEach((endpoint, result) {
      final success = result['success'] ?? false;
      final statusCode = result['status_code'];
      final responseTime = result['response_time_ms'];
      final error = result['error'];
      
      if (success) {
        print('✅ $endpoint: $statusCode (${responseTime}ms)');
      } else {
        print('❌ $endpoint: ${error ?? 'Failed'}');
      }
    });
    
    print('\n--- API Tests ---');
    final apiTests = info['api_tests'] as Map<String, dynamic>;
    apiTests.forEach((endpoint, result) {
      final success = result['success'] ?? false;
      final statusCode = result['status_code'];
      final responseTime = result['response_time_ms'];
      final error = result['error'];
      
      if (success) {
        print('✅ $endpoint: $statusCode (${responseTime}ms)');
      } else {
        print('❌ $endpoint: ${error ?? 'Failed'}');
      }
    });
    
    print('========================');
  }

  /// Quick connectivity test
  static Future<bool> quickConnectivityTest() async {
    try {
      // Test basic connectivity
      final hasBasic = await _networkUtils.hasBasicConnectivity();
      if (!hasBasic) {
        if (kDebugMode) print('❌ No basic connectivity');
        return false;
      }
      
      if (kDebugMode) print('✅ Basic connectivity OK');
      
      // Test simple HTTP request
      try {
        final response = await http.head(Uri.parse('https://www.google.com'))
            .timeout(const Duration(seconds: 5));
        
        if (response.statusCode >= 200 && response.statusCode < 500) {
          if (kDebugMode) print('✅ Internet connectivity OK');
          return true;
        } else {
          if (kDebugMode) print('⚠️ Unexpected status code: ${response.statusCode}');
          return hasBasic; // Fallback to basic connectivity
        }
      } catch (e) {
        if (kDebugMode) print('⚠️ Internet test failed: $e');
        return hasBasic; // Fallback to basic connectivity
      }
    } catch (e) {
      if (kDebugMode) print('❌ Connectivity test failed: $e');
      return false;
    }
  }

  /// Test API connectivity specifically
  static Future<bool> testApiConnectivity() async {
    try {
      if (kDebugMode) print('Testing API connectivity to: ${ApiConstants.baseUrl}');
      
      final response = await http.get(
        Uri.parse('${ApiConstants.baseUrl}${ApiConstants.loginEndpoint}'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));
      
      if (kDebugMode) {
        print('API response: ${response.statusCode}');
        print('API headers: ${response.headers}');
      }
      
      // Any response from the server means it's reachable
      return response.statusCode >= 200 && response.statusCode < 500;
    } catch (e) {
      if (kDebugMode) print('API connectivity test failed: $e');
      return false;
    }
  }
}
