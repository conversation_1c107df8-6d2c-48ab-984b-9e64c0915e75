<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Category extends Model
{
    protected $fillable = [
        'name',
        'color',
        'description',
        'icon',
        'is_active',
        'sort_order',
        'parent_id'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer'
    ];

    // Get domains count for this category
    public function getDomainsCountAttribute()
    {
        return Domain::whereJsonContains('categories', $this->id)->count();
    }

    // Get domains for this category
    public function domains()
    {
        return Domain::whereJsonContains('categories', $this->id);
    }

    // Scope for search
    public function scopeSearch($query, $term)
    {
        return $query->where('name', 'like', '%' . $term . '%');
    }
}
