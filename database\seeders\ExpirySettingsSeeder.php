<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ExpirySettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        \App\Models\ExpirySettings::create([
            'admin_email' => '<EMAIL>',
            'alert_days' => '30,15,7,1',
            'is_active' => true,
            'smtp_host' => 'smtp.gmail.com',
            'smtp_port' => 587,
            'smtp_username' => null,
            'smtp_password' => null,
            'smtp_encryption' => 'tls',
            'mail_from_address' => '<EMAIL>',
            'mail_from_name' => 'Domain CRM System'
        ]);
    }
}
