import 'package:intl/intl.dart';

class DateFormatter {
  static String formatDate(String dateString) {
    try {
      DateTime date = DateTime.parse(dateString);
      return DateFormat('dd MMM yyyy').format(date);
    } catch (e) {
      return dateString; // Return original if parsing fails
    }
  }

  static String formatDateFromDateTime(DateTime date) {
    return DateFormat('dd MMM yyyy').format(date);
  }
}