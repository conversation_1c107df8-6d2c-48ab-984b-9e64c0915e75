@extends('layouts.app')

@section('title', 'Edit Identify Domain - Domain CRM')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-edit me-2 text-warning"></i>
        Edit Identify Domain
    </h1>
    <div class="d-flex gap-2">
        <a href="{{ route('simple-domains.show', $simpleDomain) }}" class="btn btn-outline-info">
            <i class="fas fa-eye me-1"></i>
            View Details
        </a>
        <a href="{{ route('simple-domains.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>
            Back to Identify Domains
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    Edit: {{ $simpleDomain->name }}
                </h5>
            </div>
            <div class="card-body">
                <form id="editSimpleDomainForm" method="POST" action="{{ route('simple-domains.update', $simpleDomain) }}">
                    @csrf
                    @method('PUT')
                    
                    <!-- Domain Name -->
                    <div class="mb-3">
                        <label for="name" class="form-label">Domain Name *</label>
                        <div class="input-group">
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $simpleDomain->name) }}" 
                                   placeholder="example" required>
                            <button type="button" class="btn btn-outline-primary" id="checkDuplicateBtn">
                                <i class="fas fa-search"></i>
                                Check
                            </button>
                        </div>
                        <div id="duplicateCheck" class="mt-2"></div>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Categories -->
                    <div class="mb-4">
                        <label class="form-label">Categories *</label>
                        <div class="row">
                            @php
                                $oldCategories = old('categories', $simpleDomain->categories);
                            @endphp
                            @foreach($categories as $category)
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="categories[]" value="{{ $category->id }}" 
                                               id="cat{{ $category->id }}"
                                               {{ in_array($category->id, $oldCategories) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="cat{{ $category->id }}">
                                            <span class="badge" style="background-color: {{ $category->color }}">
                                                {{ $category->name }}
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        @error('categories')
                            <div class="text-danger small">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ route('simple-domains.show', $simpleDomain) }}" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save me-1"></i>
                            Update Domain
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkDuplicateBtn = document.getElementById('checkDuplicateBtn');
    const nameInput = document.getElementById('name');
    const duplicateCheck = document.getElementById('duplicateCheck');

    checkDuplicateBtn.addEventListener('click', function() {
        const domainName = nameInput.value.trim();
        
        if (!domainName) {
            duplicateCheck.innerHTML = '<div class="alert alert-warning alert-sm">Please enter a domain name first.</div>';
            return;
        }

        // Show loading state
        checkDuplicateBtn.disabled = true;
        checkDuplicateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';

        // Get CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (!csrfToken) {
            duplicateCheck.innerHTML = '<div class="alert alert-danger alert-sm">CSRF token not found. Please refresh the page.</div>';
            checkDuplicateBtn.disabled = false;
            checkDuplicateBtn.innerHTML = '<i class="fas fa-search"></i> Check';
            return;
        }

        // Make AJAX request to check for duplicates
        fetch('/simple-domains/check-duplicate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
                'Accept': 'application/json'
            },
            body: JSON.stringify({ 
                name: domainName,
                ignore_id: {{ $simpleDomain->id }} // Ignore current domain when checking duplicates
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.exists) {
                duplicateCheck.innerHTML = '<div class="alert alert-danger alert-sm"><i class="fas fa-exclamation-triangle me-1"></i>This domain name already exists!</div>';
                nameInput.classList.add('is-invalid');
            } else {
                duplicateCheck.innerHTML = '<div class="alert alert-success alert-sm"><i class="fas fa-check me-1"></i>Domain name is available!</div>';
                nameInput.classList.remove('is-invalid');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            duplicateCheck.innerHTML = '<div class="alert alert-danger alert-sm">Error checking domain. Please try again.</div>';
        })
        .finally(() => {
            // Reset button state
            checkDuplicateBtn.disabled = false;
            checkDuplicateBtn.innerHTML = '<i class="fas fa-search"></i> Check';
        });
    });

    // Domain name cleaning functionality
    function cleanDomainName(value) {
        // Remove spaces and convert to lowercase
        return value.replace(/\s+/g, '').toLowerCase();
    }

    // Auto-clean domain name as user types
    nameInput.addEventListener('input', function() {
        const originalValue = this.value;
        const cleanedValue = cleanDomainName(originalValue);
        
        // Only update if the value changed to avoid cursor jumping
        if (originalValue !== cleanedValue) {
            const cursorPosition = this.selectionStart;
            this.value = cleanedValue;
            // Restore cursor position (accounting for removed characters)
            const removedChars = originalValue.length - cleanedValue.length;
            this.setSelectionRange(cursorPosition - removedChars, cursorPosition - removedChars);
        }
        
        duplicateCheck.innerHTML = '';
        nameInput.classList.remove('is-invalid');
    });
});
</script>
@endpush