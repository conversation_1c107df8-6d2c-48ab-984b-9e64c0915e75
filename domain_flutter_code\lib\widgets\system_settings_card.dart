import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';

class SystemSettingsCard extends StatelessWidget {
  const SystemSettingsCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settingsProvider, child) {
        final systemSettings = settingsProvider.systemSettings;
        
        if (systemSettings == null) {
          return Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Center(
              child: Text('No system settings data available'),
            ),
          );
        }

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Application Information
              _buildSectionHeader(
                context,
                'Application Information',
                Icons.info_rounded,
                Colors.blue,
              ),
              const SizedBox(height: 16),
              _buildInfoCard(
                context,
                'App Name',
                systemSettings['app_name'] ?? 'Domain CRM',
                Icons.apps_rounded,
                Colors.blue,
              ),
              const SizedBox(height: 12),
              _buildInfoCard(
                context,
                'App URL',
                systemSettings['app_url'] ?? 'Not configured',
                Icons.link_rounded,
                Colors.blue,
              ),
              const SizedBox(height: 12),
              _buildInfoCard(
                context,
                'Environment',
                (systemSettings['app_env'] ?? 'production').toUpperCase(),
                Icons.cloud_rounded,
                _getEnvironmentColor(systemSettings['app_env']),
              ),
              const SizedBox(height: 12),
              _buildInfoCard(
                context,
                'Debug Mode',
                (systemSettings['app_debug'] == true) ? 'Enabled' : 'Disabled',
                Icons.bug_report_rounded,
                (systemSettings['app_debug'] == true) ? Colors.orange : Colors.green,
              ),
              
              const SizedBox(height: 24),
              
              // Mail Configuration
              _buildSectionHeader(
                context,
                'Mail Configuration',
                Icons.mail_rounded,
                Colors.green,
              ),
              const SizedBox(height: 16),
              _buildInfoCard(
                context,
                'Mail Driver',
                systemSettings['mail_driver'] ?? 'Not configured',
                Icons.send_rounded,
                Colors.green,
              ),
              
              const SizedBox(height: 24),
              
              // System Configuration
              _buildSectionHeader(
                context,
                'System Configuration',
                Icons.settings_rounded,
                Colors.purple,
              ),
              const SizedBox(height: 16),
              _buildInfoCard(
                context,
                'Timezone',
                systemSettings['timezone'] ?? 'UTC',
                Icons.access_time_rounded,
                Colors.purple,
              ),
              
              const SizedBox(height: 24),
              
              // Status Indicators
              _buildSectionHeader(
                context,
                'System Status',
                Icons.health_and_safety_rounded,
                Colors.teal,
              ),
              const SizedBox(height: 16),
              
              Row(
                children: [
                  Expanded(
                    child: _buildStatusCard(
                      context,
                      'API Status',
                      'Online',
                      Icons.api_rounded,
                      Colors.green,
                      true,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatusCard(
                      context,
                      'Database',
                      'Connected',
                      Icons.storage_rounded,
                      Colors.green,
                      true,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              Row(
                children: [
                  Expanded(
                    child: _buildStatusCard(
                      context,
                      'Mail Service',
                      systemSettings['mail_driver'] != null ? 'Configured' : 'Not Configured',
                      Icons.email_rounded,
                      systemSettings['mail_driver'] != null ? Colors.green : Colors.orange,
                      systemSettings['mail_driver'] != null,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatusCard(
                      context,
                      'Security',
                      (systemSettings['app_debug'] == true) ? 'Debug Mode' : 'Secure',
                      Icons.security_rounded,
                      (systemSettings['app_debug'] == true) ? Colors.orange : Colors.green,
                      !(systemSettings['app_debug'] == true),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 24),
              
              // Refresh Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: settingsProvider.isLoading 
                      ? null 
                      : () => settingsProvider.loadSystemSettings(),
                  icon: settingsProvider.isLoading
                      ? const SizedBox(
                          height: 16,
                          width: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.refresh_rounded),
                  label: Text(
                    settingsProvider.isLoading ? 'Refreshing...' : 'Refresh System Info',
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoCard(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusCard(
    BuildContext context,
    String label,
    String status,
    IconData icon,
    Color color,
    bool isHealthy,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 6),
              Text(
                status,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getEnvironmentColor(String? environment) {
    switch (environment?.toLowerCase()) {
      case 'production':
        return Colors.green;
      case 'staging':
        return Colors.orange;
      case 'development':
      case 'local':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
}
